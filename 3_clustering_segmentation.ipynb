{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Notebook 3 : Clustering & Segmentation\n", "\n", "## Objectif\n", "Appliquer des algorithmes de clustering (K-Means, éventuellement DBSCAN ou Agglomératif) sur les données normalisées. Déterminer le nombre optimal de clusters, analyser les profils par segment, et visualiser les résultats.\n", "\n", "---\n", "\n", "**Auteur :** <PERSON><PERSON>  \n", "**Date :** 3 juin 2025  \n", "**Projet :** Segmentation client Olist  "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Chargement des données préparées\n", "\n", "### 1.1 Import des librairies de clustering"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Imports spécifiques pour ce notebook\n", "import os\n", "import json\n", "from datetime import datetime\n", "import joblib\n", "\n", "# Clustering et métriques\n", "from sklearn.cluster import KMeans, DBSCAN, AgglomerativeClustering\n", "from sklearn.decomposition import PCA\n", "from sklearn.metrics import silhouette_score, calinski_harabasz_score\n", "from sklearn.preprocessing import StandardScaler\n", "from itertools import combinations\n", "from scipy.cluster.hierarchy import dendrogram, linkage\n", "from scipy import stats\n", "\n", "# Imports locaux - modules utils optimisés\n", "from utils.core import (\n", "    init_notebook, SEED, PROJECT_ROOT, REPORTS_DIR,\n", "    # Les imports de base sont déjà dans core\n", "    pd, np, plt, sns\n", ")\n", "from utils.clustering import (\n", "    find_optimal_k,  # au lieu de find_optimal_k_elbow\n", "    perform_kmeans,\n", "    perform_dbscan,\n", "    calculate_clustering_metrics,\n", "    analyze_clusters,\n", "    compare_clustering_algorithms\n", ")  # Suppression de la parenthèse supplémentaire ici\n", "from utils.clustering_visualization import (\n", "    plot_elbow_curve,\n", "    plot_clusters_2d,\n", "    plot_cluster_profiles,\n", "    plot_cluster_feature_distributions,\n", "    plot_cluster_sizes,\n", "    export_figure\n", ")\n", "from utils.data_tools import load_data, export_artifact\n", "from utils.save_load import save_results, load_results\n", "\n", "# Configuration du notebook avec le module core\n", "init_notebook(\n", "    notebook_file_path=\"3_clustering_segmentation.ipynb\",\n", "    style=\"whitegrid\",\n", "    figsize=(12, 8),\n", "    random_seed=SEED,\n", "    setup=True,\n", "    check_deps=True\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.2 Import du jeu de données normalisé"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Chargement des données préparées du Notebook 2\n", "print(\"🔄 Chargement des données préparées pour le clustering...\")\n", "\n", "# Chemins des fichiers générés par le notebook 2\n", "data_path_scaled = 'data/processed/2_01_features_scaled_clustering.csv'\n", "data_path_with_ids = 'data/processed/2_02_features_scaled_with_ids.csv'\n", "data_path_complete = 'data/processed/2_03_rfm_enriched_complete.csv'\n", "\n", "try:\n", "    # Chargement des datasets\n", "    X_scaled = load_results(data_path_scaled)\n", "    X_with_ids = load_results(data_path_with_ids)\n", "    df_complete = load_results(data_path_complete)\n", "\n", "    print(f\"✅ Dataset pour clustering : {X_scaled.shape}\")\n", "    print(f\"   Variables : {list(X_scaled.columns)}\")\n", "    print(f\"\\n✅ Dataset avec IDs : {X_with_ids.shape}\")\n", "    print(f\"✅ Dataset complet : {df_complete.shape}\")\n", "\n", "    # Vérification de la cohérence\n", "    assert len(X_scaled) == len(X_with_ids) == len(df_complete), \"Tailles incohérentes entre les datasets\"\n", "    print(f\"\\n✅ Cohérence vérifiée : {len(X_scaled)} clients dans tous les datasets\")\n", "\n", "except FileNotFoundError as e:\n", "    print(f\"❌ Erreur : Fichier non trouvé - {e}\")\n", "    print(\"💡 Assurez-vous d'avoir exécuté le Notebook 2 (Feature Engineering) avant ce notebook.\")\n", "    raise\n", "except Exception as e:\n", "    print(f\"❌ Erreur lors du chargement : {e}\")\n", "    raise"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.3 Rappel des variables utilisées pour la segmentation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Vérification et rappel des variables\n", "print(\"📊 Variables utilisées pour la segmentation :\")\n", "for i, col in enumerate(X_scaled.columns, 1):\n", "    print(f\"{i:2d}. {col}\")\n", "\n", "print(f\"\\n📈 Statistiques descriptives (données normalisées) :\")\n", "display(X_scaled.describe().round(3))\n", "\n", "# Vérification de la normalisation\n", "print(f\"\\n🔍 Vérification de la normalisation :\")\n", "\n", "# Identification des variables à vérifier (écart-type > 0)\n", "variables_to_check = X_scaled.columns[X_scaled.std() > 0].tolist()\n", "constant_variables = X_scaled.columns[X_scaled.std() == 0].tolist()\n", "\n", "print(f\"   Variables à vérifier : {len(variables_to_check)}\")\n", "print(f\"   Variables constantes : {len(constant_variables)}\")\n", "\n", "# Vérification uniquement sur les variables non constantes\n", "mean_max = X_scaled[variables_to_check].mean().abs().max()\n", "std_mean = X_scaled[variables_to_check].std().mean()\n", "\n", "print(f\"   Moyennes (doivent être ~0) : {mean_max:.6f}\")\n", "print(f\"   Écarts-types (doivent être ~1) : {std_mean:.6f}\")\n", "\n", "# Validation de la normalisation\n", "if mean_max < 1e-10 and 0.9 <= std_mean <= 1.1:\n", "    print(\"✅ Normalisation correcte\")\n", "else:\n", "    print(\"⚠️ Attention : normalisation potentiellement incorrecte\")\n", "\n", "# Affichage des variables constantes\n", "if constant_variables:\n", "    print(\"\\n📊 Variables constantes (non normalisées) :\")\n", "    for var in constant_variables:\n", "        print(f\"   - {var} : valeur = {X_scaled[var].iloc[0]}\")\n", "\n", "# Vérification des valeurs manquantes\n", "missing_values = X_scaled.isnull().sum().sum()\n", "print(f\"\\n🔍 Valeurs manquantes : {missing_values}\")\n", "if missing_values == 0:\n", "    print(\"✅ Aucune valeur manquante\")\n", "else:\n", "    print(\"⚠️ Attention : valeurs manquantes détectées\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Recherche du nombre optimal de clusters\n", "\n", "### 2.1 Méthode du coude (Elbow Method)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Méthode du coude pour déterminer k optimal\n", "print(\"🔍 Recherche du nombre optimal de clusters avec la méthode du coude...\")\n", "\n", "# Calcul de l'inertie pour différentes valeurs de k\n", "k_range = range(2, 11)\n", "inertias = []\n", "silhouette_scores = []\n", "\n", "# Optimisation : utiliser un sous-ensemble pour le score de silhouette\n", "sample_size = min(10000, len(X_scaled))  # Limiter à 10000 points max\n", "if sample_size < len(X_scaled):\n", "    print(f\"📊 Utilisation d'un échantillon de {sample_size} points pour le score de silhouette\")\n", "    X_sample = X_scaled.sample(n=sample_size, random_state=SEED)\n", "else:\n", "    X_sample = X_scaled\n", "\n", "print(\"Calcul en cours...\")\n", "for k in k_range:\n", "    print(f\"  K = {k}\", end=\" \")\n", "\n", "    # K-Means avec paramètres optimisés\n", "    kmeans = KMeans(\n", "        n_clusters=k,\n", "        random_state=SEED,\n", "        n_init=5,  # Réduit de 10 à 5\n", "        max_iter=300,\n", "        algorithm='elkan'  # Plus rapide que 'auto' pour les datasets denses\n", "    )\n", "    cluster_labels = kmeans.fit_predict(X_scaled)\n", "\n", "    # Calcul du score de silhouette sur l'échantillon\n", "    sample_labels = kmeans.predict(X_sample)\n", "    silhouette_avg = silhouette_score(X_sample, sample_labels)\n", "\n", "    inertias.append(kmeans.inertia_)\n", "    silhouette_scores.append(silhouette_avg)\n", "\n", "    print(f\"(Silhouette: {silhouette_avg:.3f})\")\n", "\n", "print(\"\\n✅ Calculs terminés\")\n", "\n", "# Utilisation du module de visualisation optimisé\n", "fig = plot_elbow_curve(k_range, inertias, silhouette_scores)\n", "export_figure(fig, notebook_name=\"3\", export_number=1, base_name=\"elbow_curve\")\n", "\n", "# Affichage des résultats\n", "print(f\"\\n📊 Inerties par k : {dict(zip(k_range, [round(i, 2) for i in inertias]))}\")\n", "print(f\"📊 Scores silhouette : {dict(zip(k_range, [round(s, 3) for s in silhouette_scores]))}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.2 Score de silhouette pour chaque k"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calcul des métriques complémentaires\n", "print(\"\\n🔍 Calcul des métriques complémentaires...\")\n", "\n", "calinski_scores = []\n", "\n", "for k in k_range:\n", "    # Recalcul pour obtenir les labels (optimisation possible)\n", "    kmeans = KMeans(n_clusters=k, random_state=SEED, n_init=10, max_iter=300)\n", "    cluster_labels = kmeans.fit_predict(X_scaled)\n", "\n", "    # Score de Calinski-<PERSON><PERSON><PERSON>\n", "    calinski_score = calinski_harabasz_score(X_scaled, cluster_labels)\n", "    calinski_scores.append(calinski_score)\n", "\n", "# Visualisation comparative des métriques\n", "fig, axes = plt.subplots(1, 3, figsize=(18, 6))\n", "\n", "# Score de silhouette\n", "axes[0].plot(k_range, silhouette_scores, 'go-', linewidth=2, markersize=8)\n", "axes[0].set_title('<PERSON> de Si<PERSON>hou<PERSON> vs <PERSON>')\n", "axes[0].set_xlabel('Nombre de clusters (K)')\n", "axes[0].set_ylabel('Score de Silhouette')\n", "axes[0].grid(True, alpha=0.3)\n", "axes[0].set_ylim(0, max(silhouette_scores) * 1.1)\n", "\n", "# Score de Calinski-<PERSON><PERSON><PERSON>\n", "axes[1].plot(k_range, calinski_scores, 'mo-', linewidth=2, markersize=8)\n", "axes[1].set_title('<PERSON> <PERSON> Cal<PERSON>ki-Hara<PERSON>z vs K')\n", "axes[1].set_xlabel('Nombre de clusters (K)')\n", "axes[1].set_ylabel('Score de Calinski-Harabasz')\n", "axes[1].grid(True, alpha=0.3)\n", "\n", "# Comparaison des métriques normalisées\n", "ax2 = axes[2]\n", "# Normalisation des métriques pour comparaison\n", "inertias_norm = np.array(inertias) / max(inertias)\n", "silhouette_norm = np.array(silhouette_scores) / max(silhouette_scores)\n", "\n", "ax2.plot(k_range, 1 - inertias_norm, 'b-', label='1 - Inertie (norm.)', linewidth=2, marker='o')\n", "ax2.plot(k_range, silhouette_norm, 'g-', label='Silhouette (norm.)', linewidth=2, marker='s')\n", "ax2.set_xlabel('Nombre de clusters (K)')\n", "ax2.set_ylabel('Score normalisé')\n", "ax2.set_title('Comparaison des métriques')\n", "ax2.legend()\n", "ax2.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "export_figure(plt.gcf(), notebook_name=\"3\", export_number=2, base_name=\"clustering_metrics\")\n", "plt.show()\n", "\n", "# Identification du k optimal\n", "optimal_k_silhouette = k_range[np.argmax(silhouette_scores)]\n", "optimal_k_calinski = k_range[np.argmax(calinski_scores)]\n", "\n", "print(f\"\\n📊 Résultats de l'optimisation :\")\n", "print(f\"   K optimal selon Silhouette : {optimal_k_silhouette} (score = {max(silhouette_scores):.3f})\")\n", "print(f\"   K optimal selon Calinski-Harabasz : {optimal_k_calinski} (score = {max(calinski_scores):.0f})\")\n", "print(f\"   Scores <PERSON><PERSON>ki : {dict(zip(k_range, [round(c, 1) for c in calinski_scores]))}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.3 Visualisation conjointe et choix justifié de k"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Synthèse pour le choix de k\n", "print(\"\\n📋 Synthèse pour le choix du nombre optimal de clusters\")\n", "\n", "# Tableau de synthèse\n", "results_df = pd.DataFrame({\n", "    'K': k_range,\n", "    'Inertie': inertias,\n", "    'Silhouette': silhouette_scores,\n", "    'Cal<PERSON><PERSON>_Harabasz': ca<PERSON><PERSON>_scores\n", "})\n", "\n", "# Ajout des variations relatives\n", "results_df['Inertie_reduction_%'] = results_df['Inertie'].pct_change().fillna(0).abs() * 100\n", "results_df['Silhouette_rank'] = results_df['Silhouette'].rank(ascending=False)\n", "results_df['<PERSON><PERSON><PERSON>_rank'] = results_df['<PERSON><PERSON><PERSON>_Harabasz'].rank(ascending=False)\n", "\n", "print(\"\\n📊 Tableau de synthèse pour le choix de K :\")\n", "display(results_df.round(3))\n", "\n", "# Analyse automatique du coude\n", "# Calcul de la dérivée seconde pour identifier le coude\n", "if len(inertias) >= 3:\n", "    second_derivatives = np.diff(np.diff(inertias))\n", "    # Le coude correspond au maximum de la dérivée seconde\n", "    elbow_idx = np.argmax(second_derivatives) + 2  # +2 car on a perdu 2 points avec diff\n", "    optimal_k_elbow = k_range[elbow_idx] if elbow_idx < len(k_range) else k_range[-1]\n", "else:\n", "    optimal_k_elbow = k_range[len(k_range)//2]  # Valeur par défaut\n", "\n", "# Recommandations basées sur les métriques\n", "print(\"\\n=== 🎯 RECOMMANDATIONS ===\")\n", "print(f\"📈 Méthode du coude : K = {optimal_k_elbow}\")\n", "print(f\"🎭 Score de silhouette optimal : K = {optimal_k_silhouette} (score = {max(silhouette_scores):.3f})\")\n", "print(f\"📊 Score de Calinski-Harabasz optimal : K = {optimal_k_calinski} (score = {max(calinski_scores):.0f})\")\n", "\n", "# Choix final basé sur la convergence des métriques\n", "# Logique de décision : privilégier la silhouette si co<PERSON><PERSON>e, sinon consensus\n", "if optimal_k_silhouette == optimal_k_ca<PERSON>ki:\n", "    optimal_k = optimal_k_silhouette\n", "    justification = \"Convergence parfaite entre silhouette et Calinski-Harabasz\"\n", "elif abs(optimal_k_silhouette - optimal_k_elbow) <= 1:\n", "    optimal_k = optimal_k_silhouette\n", "    justification = \"Sil<PERSON>ette cohérente avec la méthode du coude\"\n", "else:\n", "    # Prendre la médiane des recommandations\n", "    recommendations = [optimal_k_elbow, optimal_k_silhouette, optimal_k_calinski]\n", "    optimal_k = int(np.median(recommendations))\n", "    justification = \"Consensus entre les différentes métriques\"\n", "\n", "print(f\"\\n🎯 CHOIX FINAL : K = {optimal_k}\")\n", "print(f\"📝 Justification : {justification}\")\n", "print(f\"\\n✅ Nombre de clusters sélectionné pour la suite : {optimal_k}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.4 Pré-analyse des clusters"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cluster_labels = kmeans.predict(X_scaled)\n", "df_complete_clustered = df_complete.copy()\n", "df_complete_clustered['cluster'] = cluster_labels\n", "\n", "print(\"\\n🔍 Analyse détaillée des clusters...\")\n", "\n", "# 5.1 Analyse des caractéristiques par cluster\n", "print(\"\\n📊 Caractéristiques moyennes par cluster :\")\n", "cluster_analysis = df_complete_clustered.groupby('cluster').agg({\n", "    'recency': 'mean',\n", "    'frequency': 'mean',\n", "    'monetary': 'mean',\n", "    'customer_lifespan_days': 'mean',\n", "    'days_since_first_order': 'mean',\n", "    'total_amount': 'mean',\n", "    'avg_order_value': 'mean',\n", "    'total_orders': 'mean',\n", "    'purchase_frequency': 'mean',\n", "    'order_count': 'mean',\n", "    'montant_moyen': 'mean'\n", "}).round(2)\n", "\n", "print(cluster_analysis)\n", "\n", "# 5.2 Visualisation des distributions\n", "print(\"\\n📈 Visualisation des distributions par cluster...\")\n", "\n", "# Sélection des variables importantes pour la visualisation\n", "key_variables = ['recency', 'monetary', 'total_amount', 'avg_order_value']\n", "\n", "# Création des boxplots\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "axes = axes.ravel()\n", "\n", "for idx, var in enumerate(key_variables):\n", "    sns.boxplot(x='cluster', y=var, data=df_complete_clustered, ax=axes[idx])\n", "    axes[idx].set_title(f'Distribution de {var} par cluster')\n", "    axes[idx].set_xlabel('Cluster')\n", "    axes[idx].set_ylabel(var)\n", "\n", "plt.tight_layout()\n", "export_figure(fig, notebook_name=\"3\", export_number=3, base_name=\"cluster_distributions\")\n", "\n", "# 5.3 Détection des outliers\n", "print(\"\\n🔍 Analyse des outliers...\")\n", "\n", "# Calcul des z-scores\n", "z_scores = stats.zscore(X_scaled)\n", "outliers = (abs(z_scores) > 3).any(axis=1)\n", "outlier_percentage = (outliers.sum() / len(X_scaled)) * 100\n", "\n", "print(f\"Nombre d'outliers potentiels : {outliers.sum()} ({outlier_percentage:.1f}% des clients)\")\n", "\n", "# Distribution des outliers par cluster\n", "outlier_clusters = df_complete_clustered.loc[outliers, 'cluster'].value_counts()\n", "print(\"\\nDistribution des outliers par cluster :\")\n", "for cluster, count in outlier_clusters.items():\n", "    percentage = (count / len(df_complete_clustered[df_complete_clustered['cluster'] == cluster])) * 100\n", "    print(f\"Cluster {cluster}: {count} outliers ({percentage:.1f}% du cluster)\")\n", "\n", "# 5.4 Analyse des variables catégorielles\n", "print(\"\\n📊 Analyse des variables catégorielles par cluster...\")\n", "\n", "categorical_vars = ['customer_age_category']  # Seule variable catégorielle disponible\n", "\n", "for var in categorical_vars:\n", "    if var in df_complete_clustered.columns:\n", "        print(f\"\\nDistribution de {var} par cluster :\")\n", "        cross_tab = pd.crosstab(df_complete_clustered['cluster'], df_complete_clustered[var], normalize='index') * 100\n", "        print(cross_tab.round(1))\n", "\n", "# 5.5 Synthèse des clusters\n", "print(\"\\n📝 Synthèse des clusters :\")\n", "\n", "for cluster in df_complete_clustered['cluster'].unique():\n", "    cluster_data = df_complete_clustered[df_complete_clustered['cluster'] == cluster]\n", "    print(f\"\\nCluster {cluster} :\")\n", "    print(f\"Taille : {len(cluster_data)} clients ({len(cluster_data)/len(df_complete_clustered)*100:.1f}%)\")\n", "    print(\"Caractéristiques principales :\")\n", "    for var in key_variables:\n", "        mean_val = cluster_data[var].mean()\n", "        print(f\"- {var} : {mean_val:.2f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"\\n🔍 Analyse détaillée des clusters...\")\n", "\n", "# 5.1 Analyse des caractéristiques par cluster\n", "print(\"\\n📊 Caractéristiques moyennes par cluster :\")\n", "cluster_analysis = df_complete_clustered.groupby('cluster').agg({\n", "    'recency': 'mean',\n", "    'frequency': 'mean',\n", "    'monetary': 'mean',\n", "    'customer_lifespan_days': 'mean',\n", "    'days_since_first_order': 'mean',\n", "    'total_amount': 'mean',\n", "    'avg_order_value': 'mean',\n", "    'total_orders': 'mean',\n", "    'purchase_frequency': 'mean',\n", "    'order_count': 'mean',\n", "    'montant_moyen': 'mean'\n", "}).round(2)\n", "\n", "print(cluster_analysis)\n", "\n", "# 5.2 Visualisation des distributions\n", "print(\"\\n📈 Visualisation des distributions par cluster...\")\n", "\n", "# Sélection des variables importantes pour la visualisation\n", "key_variables = ['recency', 'monetary', 'total_amount', 'avg_order_value']\n", "\n", "# Création des boxplots\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "axes = axes.ravel()\n", "\n", "for idx, var in enumerate(key_variables):\n", "    sns.boxplot(x='cluster', y=var, data=df_complete_clustered, ax=axes[idx])\n", "    axes[idx].set_title(f'Distribution de {var} par cluster')\n", "    axes[idx].set_xlabel('Cluster')\n", "    axes[idx].set_ylabel(var)\n", "\n", "plt.tight_layout()\n", "export_figure(fig, notebook_name=\"3\", export_number=3, base_name=\"cluster_distributions\")\n", "\n", "# 5.3 Détection des outliers\n", "print(\"\\n🔍 Analyse des outliers...\")\n", "\n", "# Calcul des z-scores\n", "z_scores = stats.zscore(X_scaled)\n", "outliers = (abs(z_scores) > 3).any(axis=1)\n", "outlier_percentage = (outliers.sum() / len(X_scaled)) * 100\n", "\n", "print(f\"Nombre d'outliers potentiels : {outliers.sum()} ({outlier_percentage:.1f}% des clients)\")\n", "\n", "# Distribution des outliers par cluster\n", "outlier_clusters = df_complete_clustered.loc[outliers, 'cluster'].value_counts()\n", "print(\"\\nDistribution des outliers par cluster :\")\n", "for cluster, count in outlier_clusters.items():\n", "    percentage = (count / len(df_complete_clustered[df_complete_clustered['cluster'] == cluster])) * 100\n", "    print(f\"Cluster {cluster}: {count} outliers ({percentage:.1f}% du cluster)\")\n", "\n", "# 5.4 Analyse des variables catégorielles\n", "print(\"\\n📊 Analyse des variables catégorielles par cluster...\")\n", "\n", "categorical_vars = ['customer_age_category']  # Seule variable catégorielle disponible\n", "\n", "for var in categorical_vars:\n", "    if var in df_complete_clustered.columns:\n", "        print(f\"\\nDistribution de {var} par cluster :\")\n", "        cross_tab = pd.crosstab(df_complete_clustered['cluster'], df_complete_clustered[var], normalize='index') * 100\n", "        print(cross_tab.round(1))\n", "\n", "# 5.5 Synthèse des clusters\n", "print(\"\\n📝 Synthèse des clusters :\")\n", "\n", "for cluster in df_complete_clustered['cluster'].unique():\n", "    cluster_data = df_complete_clustered[df_complete_clustered['cluster'] == cluster]\n", "    print(f\"\\nCluster {cluster} :\")\n", "    print(f\"Taille : {len(cluster_data)} clients ({len(cluster_data)/len(df_complete_clustered)*100:.1f}%)\")\n", "    print(\"Caractéristiques principales :\")\n", "    for var in key_variables:\n", "        mean_val = cluster_data[var].mean()\n", "        print(f\"- {var} : {mean_val:.2f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"\\n🔍 Analyse détaillée des clusters...\")\n", "\n", "# 5.1 Analyse des caractéristiques par cluster\n", "print(\"\\n📊 Caractéristiques moyennes par cluster :\")\n", "cluster_analysis = df_complete_clustered.groupby('cluster').agg({\n", "    'recency': 'mean',\n", "    'frequency': 'mean',\n", "    'monetary': 'mean',\n", "    'customer_lifespan_days': 'mean',\n", "    'days_since_first_order': 'mean',\n", "    'total_amount': 'mean',\n", "    'avg_order_value': 'mean',\n", "    'total_orders': 'mean',\n", "    'purchase_frequency': 'mean',\n", "    'order_count': 'mean',\n", "    'montant_moyen': 'mean'\n", "}).round(2)\n", "\n", "print(cluster_analysis)\n", "\n", "# 5.2 Visualisation des distributions\n", "print(\"\\n📈 Visualisation des distributions par cluster...\")\n", "\n", "# Sélection des variables importantes pour la visualisation\n", "key_variables = ['recency', 'monetary', 'total_amount', 'avg_order_value']\n", "\n", "# Création des boxplots\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "axes = axes.ravel()\n", "\n", "for idx, var in enumerate(key_variables):\n", "    sns.boxplot(x='cluster', y=var, data=df_complete_clustered, ax=axes[idx])\n", "    axes[idx].set_title(f'Distribution de {var} par cluster')\n", "    axes[idx].set_xlabel('Cluster')\n", "    axes[idx].set_ylabel(var)\n", "\n", "plt.tight_layout()\n", "export_figure(fig, notebook_name=\"3\", export_number=3, base_name=\"cluster_distributions\")\n", "\n", "# 5.3 Détection des outliers\n", "print(\"\\n🔍 Analyse des outliers...\")\n", "\n", "# Calcul des z-scores\n", "z_scores = stats.zscore(X_scaled)\n", "outliers = (abs(z_scores) > 3).any(axis=1)\n", "outlier_percentage = (outliers.sum() / len(X_scaled)) * 100\n", "\n", "print(f\"Nombre d'outliers potentiels : {outliers.sum()} ({outlier_percentage:.1f}% des clients)\")\n", "\n", "# Distribution des outliers par cluster\n", "outlier_clusters = df_complete_clustered.loc[outliers, 'cluster'].value_counts()\n", "print(\"\\nDistribution des outliers par cluster :\")\n", "for cluster, count in outlier_clusters.items():\n", "    percentage = (count / len(df_complete_clustered[df_complete_clustered['cluster'] == cluster])) * 100\n", "    print(f\"Cluster {cluster}: {count} outliers ({percentage:.1f}% du cluster)\")\n", "\n", "# 5.4 Analyse des variables catégorielles\n", "print(\"\\n📊 Analyse des variables catégorielles par cluster...\")\n", "\n", "categorical_vars = ['customer_age_category']  # Seule variable catégorielle disponible\n", "\n", "for var in categorical_vars:\n", "    if var in df_complete_clustered.columns:\n", "        print(f\"\\nDistribution de {var} par cluster :\")\n", "        cross_tab = pd.crosstab(df_complete_clustered['cluster'], df_complete_clustered[var], normalize='index') * 100\n", "        print(cross_tab.round(1))\n", "\n", "# 5.5 Synthèse des clusters\n", "print(\"\\n📝 Synthèse des clusters :\")\n", "\n", "for cluster in df_complete_clustered['cluster'].unique():\n", "    cluster_data = df_complete_clustered[df_complete_clustered['cluster'] == cluster]\n", "    print(f\"\\nCluster {cluster} :\")\n", "    print(f\"Taille : {len(cluster_data)} clients ({len(cluster_data)/len(df_complete_clustered)*100:.1f}%)\")\n", "    print(\"Caractéristiques principales :\")\n", "    for var in key_variables:\n", "        mean_val = cluster_data[var].mean()\n", "        print(f\"- {var} : {mean_val:.2f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"\\n🔍 Analyse détaillée des clusters...\")\n", "\n", "# 5.1 Analyse des caractéristiques par cluster\n", "print(\"\\n📊 Caractéristiques moyennes par cluster :\")\n", "cluster_analysis = df_complete_clustered.groupby('cluster').agg({\n", "    'recency': 'mean',\n", "    'frequency': 'mean',\n", "    'monetary': 'mean',\n", "    'customer_lifespan_days': 'mean',\n", "    'days_since_first_order': 'mean',\n", "    'total_amount': 'mean',\n", "    'avg_order_value': 'mean',\n", "    'total_orders': 'mean',\n", "    'purchase_frequency': 'mean',\n", "    'order_count': 'mean',\n", "    'montant_moyen': 'mean'\n", "}).round(2)\n", "\n", "print(cluster_analysis)\n", "\n", "# 5.2 Visualisation des distributions\n", "print(\"\\n📈 Visualisation des distributions par cluster...\")\n", "\n", "# Sélection des variables importantes pour la visualisation\n", "key_variables = ['recency', 'monetary', 'total_amount', 'avg_order_value']\n", "\n", "# Création des boxplots\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "axes = axes.ravel()\n", "\n", "for idx, var in enumerate(key_variables):\n", "    sns.boxplot(x='cluster', y=var, data=df_complete_clustered, ax=axes[idx])\n", "    axes[idx].set_title(f'Distribution de {var} par cluster')\n", "    axes[idx].set_xlabel('Cluster')\n", "    axes[idx].set_ylabel(var)\n", "\n", "plt.tight_layout()\n", "export_figure(fig, notebook_name=\"3\", export_number=3, base_name=\"cluster_distributions\")\n", "\n", "# 5.3 Détection des outliers\n", "print(\"\\n🔍 Analyse des outliers...\")\n", "\n", "# Calcul des z-scores\n", "z_scores = stats.zscore(X_scaled)\n", "outliers = (abs(z_scores) > 3).any(axis=1)\n", "outlier_percentage = (outliers.sum() / len(X_scaled)) * 100\n", "\n", "print(f\"Nombre d'outliers potentiels : {outliers.sum()} ({outlier_percentage:.1f}% des clients)\")\n", "\n", "# Distribution des outliers par cluster\n", "outlier_clusters = df_complete_clustered.loc[outliers, 'cluster'].value_counts()\n", "print(\"\\nDistribution des outliers par cluster :\")\n", "for cluster, count in outlier_clusters.items():\n", "    percentage = (count / len(df_complete_clustered[df_complete_clustered['cluster'] == cluster])) * 100\n", "    print(f\"Cluster {cluster}: {count} outliers ({percentage:.1f}% du cluster)\")\n", "\n", "# 5.4 Analyse des variables catégorielles\n", "print(\"\\n📊 Analyse des variables catégorielles par cluster...\")\n", "\n", "categorical_vars = ['customer_age_category']  # Seule variable catégorielle disponible\n", "\n", "for var in categorical_vars:\n", "    if var in df_complete_clustered.columns:\n", "        print(f\"\\nDistribution de {var} par cluster :\")\n", "        cross_tab = pd.crosstab(df_complete_clustered['cluster'], df_complete_clustered[var], normalize='index') * 100\n", "        print(cross_tab.round(1))\n", "\n", "# 5.5 Synthèse des clusters\n", "print(\"\\n📝 Synthèse des clusters :\")\n", "\n", "for cluster in df_complete_clustered['cluster'].unique():\n", "    cluster_data = df_complete_clustered[df_complete_clustered['cluster'] == cluster]\n", "    print(f\"\\nCluster {cluster} :\")\n", "    print(f\"Taille : {len(cluster_data)} clients ({len(cluster_data)/len(df_complete_clustered)*100:.1f}%)\")\n", "    print(\"Caractéristiques principales :\")\n", "    for var in key_variables:\n", "        mean_val = cluster_data[var].mean()\n", "        print(f\"- {var} : {mean_val:.2f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"\\n🔍 Analyse détaillée des clusters...\")\n", "\n", "# 5.1 Analyse des caractéristiques par cluster\n", "print(\"\\n📊 Caractéristiques moyennes par cluster :\")\n", "cluster_analysis = df_complete_clustered.groupby('cluster').agg({\n", "    'recency': 'mean',\n", "    'frequency': 'mean',\n", "    'monetary': 'mean',\n", "    'customer_lifespan_days': 'mean',\n", "    'days_since_first_order': 'mean',\n", "    'total_amount': 'mean',\n", "    'avg_order_value': 'mean',\n", "    'total_orders': 'mean',\n", "    'purchase_frequency': 'mean',\n", "    'order_count': 'mean',\n", "    'montant_moyen': 'mean'\n", "}).round(2)\n", "\n", "print(cluster_analysis)\n", "\n", "# 5.2 Visualisation des distributions\n", "print(\"\\n📈 Visualisation des distributions par cluster...\")\n", "\n", "# Sélection des variables importantes pour la visualisation\n", "key_variables = ['recency', 'monetary', 'total_amount', 'avg_order_value']\n", "\n", "# Création des boxplots\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "axes = axes.ravel()\n", "\n", "for idx, var in enumerate(key_variables):\n", "    sns.boxplot(x='cluster', y=var, data=df_complete_clustered, ax=axes[idx])\n", "    axes[idx].set_title(f'Distribution de {var} par cluster')\n", "    axes[idx].set_xlabel('Cluster')\n", "    axes[idx].set_ylabel(var)\n", "\n", "plt.tight_layout()\n", "export_figure(fig, notebook_name=\"3\", export_number=3, base_name=\"cluster_distributions\")\n", "\n", "# 5.3 Détection des outliers\n", "print(\"\\n🔍 Analyse des outliers...\")\n", "\n", "# Calcul des z-scores\n", "z_scores = stats.zscore(X_scaled)\n", "outliers = (abs(z_scores) > 3).any(axis=1)\n", "outlier_percentage = (outliers.sum() / len(X_scaled)) * 100\n", "\n", "print(f\"Nombre d'outliers potentiels : {outliers.sum()} ({outlier_percentage:.1f}% des clients)\")\n", "\n", "# Distribution des outliers par cluster\n", "outlier_clusters = df_complete_clustered.loc[outliers, 'cluster'].value_counts()\n", "print(\"\\nDistribution des outliers par cluster :\")\n", "for cluster, count in outlier_clusters.items():\n", "    percentage = (count / len(df_complete_clustered[df_complete_clustered['cluster'] == cluster])) * 100\n", "    print(f\"Cluster {cluster}: {count} outliers ({percentage:.1f}% du cluster)\")\n", "\n", "# 5.4 Analyse des variables catégorielles\n", "print(\"\\n📊 Analyse des variables catégorielles par cluster...\")\n", "\n", "categorical_vars = ['customer_age_category']  # Seule variable catégorielle disponible\n", "\n", "for var in categorical_vars:\n", "    if var in df_complete_clustered.columns:\n", "        print(f\"\\nDistribution de {var} par cluster :\")\n", "        cross_tab = pd.crosstab(df_complete_clustered['cluster'], df_complete_clustered[var], normalize='index') * 100\n", "        print(cross_tab.round(1))\n", "\n", "# 5.5 Synthèse des clusters\n", "print(\"\\n📝 Synthèse des clusters :\")\n", "\n", "for cluster in df_complete_clustered['cluster'].unique():\n", "    cluster_data = df_complete_clustered[df_complete_clustered['cluster'] == cluster]\n", "    print(f\"\\nCluster {cluster} :\")\n", "    print(f\"Taille : {len(cluster_data)} clients ({len(cluster_data)/len(df_complete_clustered)*100:.1f}%)\")\n", "    print(\"Caractéristiques principales :\")\n", "    for var in key_variables:\n", "        mean_val = cluster_data[var].mean()\n", "        print(f\"- {var} : {mean_val:.2f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"\\n🔍 Analyse détaillée des clusters...\")\n", "\n", "# 5.1 Analyse des caractéristiques par cluster\n", "print(\"\\n📊 Caractéristiques moyennes par cluster :\")\n", "cluster_analysis = df_complete_clustered.groupby('cluster').agg({\n", "    'recency': 'mean',\n", "    'frequency': 'mean',\n", "    'monetary': 'mean',\n", "    'customer_lifespan_days': 'mean',\n", "    'days_since_first_order': 'mean',\n", "    'total_amount': 'mean',\n", "    'avg_order_value': 'mean',\n", "    'total_orders': 'mean',\n", "    'purchase_frequency': 'mean',\n", "    'order_count': 'mean',\n", "    'montant_moyen': 'mean'\n", "}).round(2)\n", "\n", "print(cluster_analysis)\n", "\n", "# 5.2 Visualisation des distributions\n", "print(\"\\n📈 Visualisation des distributions par cluster...\")\n", "\n", "# Sélection des variables importantes pour la visualisation\n", "key_variables = ['recency', 'monetary', 'total_amount', 'avg_order_value']\n", "\n", "# Création des boxplots\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "axes = axes.ravel()\n", "\n", "for idx, var in enumerate(key_variables):\n", "    sns.boxplot(x='cluster', y=var, data=df_complete_clustered, ax=axes[idx])\n", "    axes[idx].set_title(f'Distribution de {var} par cluster')\n", "    axes[idx].set_xlabel('Cluster')\n", "    axes[idx].set_ylabel(var)\n", "\n", "plt.tight_layout()\n", "export_figure(fig, notebook_name=\"3\", export_number=3, base_name=\"cluster_distributions\")\n", "\n", "# 5.3 Détection des outliers\n", "print(\"\\n🔍 Analyse des outliers...\")\n", "\n", "# Calcul des z-scores\n", "z_scores = stats.zscore(X_scaled)\n", "outliers = (abs(z_scores) > 3).any(axis=1)\n", "outlier_percentage = (outliers.sum() / len(X_scaled)) * 100\n", "\n", "print(f\"Nombre d'outliers potentiels : {outliers.sum()} ({outlier_percentage:.1f}% des clients)\")\n", "\n", "# Distribution des outliers par cluster\n", "outlier_clusters = df_complete_clustered.loc[outliers, 'cluster'].value_counts()\n", "print(\"\\nDistribution des outliers par cluster :\")\n", "for cluster, count in outlier_clusters.items():\n", "    percentage = (count / len(df_complete_clustered[df_complete_clustered['cluster'] == cluster])) * 100\n", "    print(f\"Cluster {cluster}: {count} outliers ({percentage:.1f}% du cluster)\")\n", "\n", "# 5.4 Analyse des variables catégorielles\n", "print(\"\\n📊 Analyse des variables catégorielles par cluster...\")\n", "\n", "categorical_vars = ['customer_age_category']  # Seule variable catégorielle disponible\n", "\n", "for var in categorical_vars:\n", "    if var in df_complete_clustered.columns:\n", "        print(f\"\\nDistribution de {var} par cluster :\")\n", "        cross_tab = pd.crosstab(df_complete_clustered['cluster'], df_complete_clustered[var], normalize='index') * 100\n", "        print(cross_tab.round(1))\n", "\n", "# 5.5 Synthèse des clusters\n", "print(\"\\n📝 Synthèse des clusters :\")\n", "\n", "for cluster in df_complete_clustered['cluster'].unique():\n", "    cluster_data = df_complete_clustered[df_complete_clustered['cluster'] == cluster]\n", "    print(f\"\\nCluster {cluster} :\")\n", "    print(f\"Taille : {len(cluster_data)} clients ({len(cluster_data)/len(df_complete_clustered)*100:.1f}%)\")\n", "    print(\"Caractéristiques principales :\")\n", "    for var in key_variables:\n", "        mean_val = cluster_data[var].mean()\n", "        print(f\"- {var} : {mean_val:.2f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"\\n🔍 Analyse détaillée des clusters...\")\n", "\n", "# 5.1 Analyse des caractéristiques par cluster\n", "print(\"\\n📊 Caractéristiques moyennes par cluster :\")\n", "cluster_analysis = df_complete_clustered.groupby('cluster').agg({\n", "    'recency': 'mean',\n", "    'frequency': 'mean',\n", "    'monetary': 'mean',\n", "    'customer_lifespan_days': 'mean',\n", "    'days_since_first_order': 'mean',\n", "    'total_amount': 'mean',\n", "    'avg_order_value': 'mean',\n", "    'total_orders': 'mean',\n", "    'purchase_frequency': 'mean',\n", "    'order_count': 'mean',\n", "    'montant_moyen': 'mean'\n", "}).round(2)\n", "\n", "print(cluster_analysis)\n", "\n", "# 5.2 Visualisation des distributions\n", "print(\"\\n📈 Visualisation des distributions par cluster...\")\n", "\n", "# Sélection des variables importantes pour la visualisation\n", "key_variables = ['recency', 'monetary', 'total_amount', 'avg_order_value']\n", "\n", "# Création des boxplots\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "axes = axes.ravel()\n", "\n", "for idx, var in enumerate(key_variables):\n", "    sns.boxplot(x='cluster', y=var, data=df_complete_clustered, ax=axes[idx])\n", "    axes[idx].set_title(f'Distribution de {var} par cluster')\n", "    axes[idx].set_xlabel('Cluster')\n", "    axes[idx].set_ylabel(var)\n", "\n", "plt.tight_layout()\n", "export_figure(fig, notebook_name=\"3\", export_number=3, base_name=\"cluster_distributions\")\n", "\n", "# 5.3 Détection des outliers\n", "print(\"\\n🔍 Analyse des outliers...\")\n", "\n", "# Calcul des z-scores\n", "z_scores = stats.zscore(X_scaled)\n", "outliers = (abs(z_scores) > 3).any(axis=1)\n", "outlier_percentage = (outliers.sum() / len(X_scaled)) * 100\n", "\n", "print(f\"Nombre d'outliers potentiels : {outliers.sum()} ({outlier_percentage:.1f}% des clients)\")\n", "\n", "# Distribution des outliers par cluster\n", "outlier_clusters = df_complete_clustered.loc[outliers, 'cluster'].value_counts()\n", "print(\"\\nDistribution des outliers par cluster :\")\n", "for cluster, count in outlier_clusters.items():\n", "    percentage = (count / len(df_complete_clustered[df_complete_clustered['cluster'] == cluster])) * 100\n", "    print(f\"Cluster {cluster}: {count} outliers ({percentage:.1f}% du cluster)\")\n", "\n", "# 5.4 Analyse des variables catégorielles\n", "print(\"\\n📊 Analyse des variables catégorielles par cluster...\")\n", "\n", "categorical_vars = ['customer_age_category']  # Seule variable catégorielle disponible\n", "\n", "for var in categorical_vars:\n", "    if var in df_complete_clustered.columns:\n", "        print(f\"\\nDistribution de {var} par cluster :\")\n", "        cross_tab = pd.crosstab(df_complete_clustered['cluster'], df_complete_clustered[var], normalize='index') * 100\n", "        print(cross_tab.round(1))\n", "\n", "# 5.5 Synthèse des clusters\n", "print(\"\\n📝 Synthèse des clusters :\")\n", "\n", "for cluster in df_complete_clustered['cluster'].unique():\n", "    cluster_data = df_complete_clustered[df_complete_clustered['cluster'] == cluster]\n", "    print(f\"\\nCluster {cluster} :\")\n", "    print(f\"Taille : {len(cluster_data)} clients ({len(cluster_data)/len(df_complete_clustered)*100:.1f}%)\")\n", "    print(\"Caractéristiques principales :\")\n", "    for var in key_variables:\n", "        mean_val = cluster_data[var].mean()\n", "        print(f\"- {var} : {mean_val:.2f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"\\n🔍 Analyse détaillée des clusters...\")\n", "\n", "# 5.1 Analyse des caractéristiques par cluster\n", "print(\"\\n📊 Caractéristiques moyennes par cluster :\")\n", "cluster_analysis = df_complete_clustered.groupby('cluster').agg({\n", "    'recency': 'mean',\n", "    'frequency': 'mean',\n", "    'monetary': 'mean',\n", "    'customer_lifespan_days': 'mean',\n", "    'days_since_first_order': 'mean',\n", "    'total_amount': 'mean',\n", "    'avg_order_value': 'mean',\n", "    'total_orders': 'mean',\n", "    'purchase_frequency': 'mean',\n", "    'order_count': 'mean',\n", "    'montant_moyen': 'mean'\n", "}).round(2)\n", "\n", "print(cluster_analysis)\n", "\n", "# 5.2 Visualisation des distributions\n", "print(\"\\n📈 Visualisation des distributions par cluster...\")\n", "\n", "# Sélection des variables importantes pour la visualisation\n", "key_variables = ['recency', 'monetary', 'total_amount', 'avg_order_value']\n", "\n", "# Création des boxplots\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "axes = axes.ravel()\n", "\n", "for idx, var in enumerate(key_variables):\n", "    sns.boxplot(x='cluster', y=var, data=df_complete_clustered, ax=axes[idx])\n", "    axes[idx].set_title(f'Distribution de {var} par cluster')\n", "    axes[idx].set_xlabel('Cluster')\n", "    axes[idx].set_ylabel(var)\n", "\n", "plt.tight_layout()\n", "export_figure(fig, notebook_name=\"3\", export_number=3, base_name=\"cluster_distributions\")\n", "\n", "# 5.3 Détection des outliers\n", "print(\"\\n🔍 Analyse des outliers...\")\n", "\n", "# Calcul des z-scores\n", "z_scores = stats.zscore(X_scaled)\n", "outliers = (abs(z_scores) > 3).any(axis=1)\n", "outlier_percentage = (outliers.sum() / len(X_scaled)) * 100\n", "\n", "print(f\"Nombre d'outliers potentiels : {outliers.sum()} ({outlier_percentage:.1f}% des clients)\")\n", "\n", "# Distribution des outliers par cluster\n", "outlier_clusters = df_complete_clustered.loc[outliers, 'cluster'].value_counts()\n", "print(\"\\nDistribution des outliers par cluster :\")\n", "for cluster, count in outlier_clusters.items():\n", "    percentage = (count / len(df_complete_clustered[df_complete_clustered['cluster'] == cluster])) * 100\n", "    print(f\"Cluster {cluster}: {count} outliers ({percentage:.1f}% du cluster)\")\n", "\n", "# 5.4 Analyse des variables catégorielles\n", "print(\"\\n📊 Analyse des variables catégorielles par cluster...\")\n", "\n", "categorical_vars = ['customer_age_category']  # Seule variable catégorielle disponible\n", "\n", "for var in categorical_vars:\n", "    if var in df_complete_clustered.columns:\n", "        print(f\"\\nDistribution de {var} par cluster :\")\n", "        cross_tab = pd.crosstab(df_complete_clustered['cluster'], df_complete_clustered[var], normalize='index') * 100\n", "        print(cross_tab.round(1))\n", "\n", "# 5.5 Synthèse des clusters\n", "print(\"\\n📝 Synthèse des clusters :\")\n", "\n", "for cluster in df_complete_clustered['cluster'].unique():\n", "    cluster_data = df_complete_clustered[df_complete_clustered['cluster'] == cluster]\n", "    print(f\"\\nCluster {cluster} :\")\n", "    print(f\"Taille : {len(cluster_data)} clients ({len(cluster_data)/len(df_complete_clustered)*100:.1f}%)\")\n", "    print(\"Caractéristiques principales :\")\n", "    for var in key_variables:\n", "        mean_val = cluster_data[var].mean()\n", "        print(f\"- {var} : {mean_val:.2f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"\\n🔍 Analyse détaillée des clusters...\")\n", "\n", "# 5.1 Analyse des caractéristiques par cluster\n", "print(\"\\n📊 Caractéristiques moyennes par cluster :\")\n", "cluster_analysis = df_complete_clustered.groupby('cluster').agg({\n", "    'recency': 'mean',\n", "    'frequency': 'mean',\n", "    'monetary': 'mean',\n", "    'customer_lifespan_days': 'mean',\n", "    'days_since_first_order': 'mean',\n", "    'total_amount': 'mean',\n", "    'avg_order_value': 'mean',\n", "    'total_orders': 'mean',\n", "    'purchase_frequency': 'mean',\n", "    'order_count': 'mean',\n", "    'montant_moyen': 'mean'\n", "}).round(2)\n", "\n", "print(cluster_analysis)\n", "\n", "# 5.2 Visualisation des distributions\n", "print(\"\\n📈 Visualisation des distributions par cluster...\")\n", "\n", "# Sélection des variables importantes pour la visualisation\n", "key_variables = ['recency', 'monetary', 'total_amount', 'avg_order_value']\n", "\n", "# Création des boxplots\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "axes = axes.ravel()\n", "\n", "for idx, var in enumerate(key_variables):\n", "    sns.boxplot(x='cluster', y=var, data=df_complete_clustered, ax=axes[idx])\n", "    axes[idx].set_title(f'Distribution de {var} par cluster')\n", "    axes[idx].set_xlabel('Cluster')\n", "    axes[idx].set_ylabel(var)\n", "\n", "plt.tight_layout()\n", "export_figure(fig, notebook_name=\"3\", export_number=3, base_name=\"cluster_distributions\")\n", "\n", "# 5.3 Détection des outliers\n", "print(\"\\n🔍 Analyse des outliers...\")\n", "\n", "# Calcul des z-scores\n", "z_scores = stats.zscore(X_scaled)\n", "outliers = (abs(z_scores) > 3).any(axis=1)\n", "outlier_percentage = (outliers.sum() / len(X_scaled)) * 100\n", "\n", "print(f\"Nombre d'outliers potentiels : {outliers.sum()} ({outlier_percentage:.1f}% des clients)\")\n", "\n", "# Distribution des outliers par cluster\n", "outlier_clusters = df_complete_clustered.loc[outliers, 'cluster'].value_counts()\n", "print(\"\\nDistribution des outliers par cluster :\")\n", "for cluster, count in outlier_clusters.items():\n", "    percentage = (count / len(df_complete_clustered[df_complete_clustered['cluster'] == cluster])) * 100\n", "    print(f\"Cluster {cluster}: {count} outliers ({percentage:.1f}% du cluster)\")\n", "\n", "# 5.4 Analyse des variables catégorielles\n", "print(\"\\n📊 Analyse des variables catégorielles par cluster...\")\n", "\n", "categorical_vars = ['customer_age_category']  # Seule variable catégorielle disponible\n", "\n", "for var in categorical_vars:\n", "    if var in df_complete_clustered.columns:\n", "        print(f\"\\nDistribution de {var} par cluster :\")\n", "        cross_tab = pd.crosstab(df_complete_clustered['cluster'], df_complete_clustered[var], normalize='index') * 100\n", "        print(cross_tab.round(1))\n", "\n", "# 5.5 Synthèse des clusters\n", "print(\"\\n📝 Synthèse des clusters :\")\n", "\n", "for cluster in df_complete_clustered['cluster'].unique():\n", "    cluster_data = df_complete_clustered[df_complete_clustered['cluster'] == cluster]\n", "    print(f\"\\nCluster {cluster} :\")\n", "    print(f\"Taille : {len(cluster_data)} clients ({len(cluster_data)/len(df_complete_clustered)*100:.1f}%)\")\n", "    print(\"Caractéristiques principales :\")\n", "    for var in key_variables:\n", "        mean_val = cluster_data[var].mean()\n", "        print(f\"- {var} : {mean_val:.2f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"\\n🔍 Analyse détaillée des clusters...\")\n", "\n", "# 5.1 Analyse des caractéristiques par cluster\n", "print(\"\\n📊 Caractéristiques moyennes par cluster :\")\n", "cluster_analysis = df_complete_clustered.groupby('cluster').agg({\n", "    'recency': 'mean',\n", "    'frequency': 'mean',\n", "    'monetary': 'mean',\n", "    'customer_lifespan_days': 'mean',\n", "    'days_since_first_order': 'mean',\n", "    'total_amount': 'mean',\n", "    'avg_order_value': 'mean',\n", "    'total_orders': 'mean',\n", "    'purchase_frequency': 'mean',\n", "    'order_count': 'mean',\n", "    'montant_moyen': 'mean'\n", "}).round(2)\n", "\n", "print(cluster_analysis)\n", "\n", "# 5.2 Visualisation des distributions\n", "print(\"\\n📈 Visualisation des distributions par cluster...\")\n", "\n", "# Sélection des variables importantes pour la visualisation\n", "key_variables = ['recency', 'monetary', 'total_amount', 'avg_order_value']\n", "\n", "# Création des boxplots\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "axes = axes.ravel()\n", "\n", "for idx, var in enumerate(key_variables):\n", "    sns.boxplot(x='cluster', y=var, data=df_complete_clustered, ax=axes[idx])\n", "    axes[idx].set_title(f'Distribution de {var} par cluster')\n", "    axes[idx].set_xlabel('Cluster')\n", "    axes[idx].set_ylabel(var)\n", "\n", "plt.tight_layout()\n", "export_figure(fig, notebook_name=\"3\", export_number=3, base_name=\"cluster_distributions\")\n", "\n", "# 5.3 Détection des outliers\n", "print(\"\\n🔍 Analyse des outliers...\")\n", "\n", "# Calcul des z-scores\n", "z_scores = stats.zscore(X_scaled)\n", "outliers = (abs(z_scores) > 3).any(axis=1)\n", "outlier_percentage = (outliers.sum() / len(X_scaled)) * 100\n", "\n", "print(f\"Nombre d'outliers potentiels : {outliers.sum()} ({outlier_percentage:.1f}% des clients)\")\n", "\n", "# Distribution des outliers par cluster\n", "outlier_clusters = df_complete_clustered.loc[outliers, 'cluster'].value_counts()\n", "print(\"\\nDistribution des outliers par cluster :\")\n", "for cluster, count in outlier_clusters.items():\n", "    percentage = (count / len(df_complete_clustered[df_complete_clustered['cluster'] == cluster])) * 100\n", "    print(f\"Cluster {cluster}: {count} outliers ({percentage:.1f}% du cluster)\")\n", "\n", "# 5.4 Analyse des variables catégorielles\n", "print(\"\\n📊 Analyse des variables catégorielles par cluster...\")\n", "\n", "categorical_vars = ['customer_age_category']  # Seule variable catégorielle disponible\n", "\n", "for var in categorical_vars:\n", "    if var in df_complete_clustered.columns:\n", "        print(f\"\\nDistribution de {var} par cluster :\")\n", "        cross_tab = pd.crosstab(df_complete_clustered['cluster'], df_complete_clustered[var], normalize='index') * 100\n", "        print(cross_tab.round(1))\n", "\n", "# 5.5 Synthèse des clusters\n", "print(\"\\n📝 Synthèse des clusters :\")\n", "\n", "for cluster in df_complete_clustered['cluster'].unique():\n", "    cluster_data = df_complete_clustered[df_complete_clustered['cluster'] == cluster]\n", "    print(f\"\\nCluster {cluster} :\")\n", "    print(f\"Taille : {len(cluster_data)} clients ({len(cluster_data)/len(df_complete_clustered)*100:.1f}%)\")\n", "    print(\"Caractéristiques principales :\")\n", "    for var in key_variables:\n", "        mean_val = cluster_data[var].mean()\n", "        print(f\"- {var} : {mean_val:.2f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"\\n🔍 Analyse détaillée des clusters...\")\n", "\n", "# 5.1 Analyse des caractéristiques par cluster\n", "print(\"\\n📊 Caractéristiques moyennes par cluster :\")\n", "cluster_analysis = df_complete_clustered.groupby('cluster').agg({\n", "    'recency': 'mean',\n", "    'frequency': 'mean',\n", "    'monetary': 'mean',\n", "    'customer_lifespan_days': 'mean',\n", "    'days_since_first_order': 'mean',\n", "    'total_amount': 'mean',\n", "    'avg_order_value': 'mean',\n", "    'total_orders': 'mean',\n", "    'purchase_frequency': 'mean',\n", "    'order_count': 'mean',\n", "    'montant_moyen': 'mean'\n", "}).round(2)\n", "\n", "print(cluster_analysis)\n", "\n", "# 5.2 Visualisation des distributions\n", "print(\"\\n📈 Visualisation des distributions par cluster...\")\n", "\n", "# Sélection des variables importantes pour la visualisation\n", "key_variables = ['recency', 'monetary', 'total_amount', 'avg_order_value']\n", "\n", "# Création des boxplots\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "axes = axes.ravel()\n", "\n", "for idx, var in enumerate(key_variables):\n", "    sns.boxplot(x='cluster', y=var, data=df_complete_clustered, ax=axes[idx])\n", "    axes[idx].set_title(f'Distribution de {var} par cluster')\n", "    axes[idx].set_xlabel('Cluster')\n", "    axes[idx].set_ylabel(var)\n", "\n", "plt.tight_layout()\n", "export_figure(fig, notebook_name=\"3\", export_number=3, base_name=\"cluster_distributions\")\n", "\n", "# 5.3 Détection des outliers\n", "print(\"\\n🔍 Analyse des outliers...\")\n", "\n", "# Calcul des z-scores\n", "z_scores = stats.zscore(X_scaled)\n", "outliers = (abs(z_scores) > 3).any(axis=1)\n", "outlier_percentage = (outliers.sum() / len(X_scaled)) * 100\n", "\n", "print(f\"Nombre d'outliers potentiels : {outliers.sum()} ({outlier_percentage:.1f}% des clients)\")\n", "\n", "# Distribution des outliers par cluster\n", "outlier_clusters = df_complete_clustered.loc[outliers, 'cluster'].value_counts()\n", "print(\"\\nDistribution des outliers par cluster :\")\n", "for cluster, count in outlier_clusters.items():\n", "    percentage = (count / len(df_complete_clustered[df_complete_clustered['cluster'] == cluster])) * 100\n", "    print(f\"Cluster {cluster}: {count} outliers ({percentage:.1f}% du cluster)\")\n", "\n", "# 5.4 Analyse des variables catégorielles\n", "print(\"\\n📊 Analyse des variables catégorielles par cluster...\")\n", "\n", "categorical_vars = ['customer_age_category']  # Seule variable catégorielle disponible\n", "\n", "for var in categorical_vars:\n", "    if var in df_complete_clustered.columns:\n", "        print(f\"\\nDistribution de {var} par cluster :\")\n", "        cross_tab = pd.crosstab(df_complete_clustered['cluster'], df_complete_clustered[var], normalize='index') * 100\n", "        print(cross_tab.round(1))\n", "\n", "# 5.5 Synthèse des clusters\n", "print(\"\\n📝 Synthèse des clusters :\")\n", "\n", "for cluster in df_complete_clustered['cluster'].unique():\n", "    cluster_data = df_complete_clustered[df_complete_clustered['cluster'] == cluster]\n", "    print(f\"\\nCluster {cluster} :\")\n", "    print(f\"Taille : {len(cluster_data)} clients ({len(cluster_data)/len(df_complete_clustered)*100:.1f}%)\")\n", "    print(\"Caractéristiques principales :\")\n", "    for var in key_variables:\n", "        mean_val = cluster_data[var].mean()\n", "        print(f\"- {var} : {mean_val:.2f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"\\n🔍 Analyse détaillée des clusters...\")\n", "\n", "# 5.1 Analyse des caractéristiques par cluster\n", "print(\"\\n📊 Caractéristiques moyennes par cluster :\")\n", "cluster_analysis = df_complete_clustered.groupby('cluster').agg({\n", "    'recency': 'mean',\n", "    'frequency': 'mean',\n", "    'monetary': 'mean',\n", "    'customer_lifespan_days': 'mean',\n", "    'days_since_first_order': 'mean',\n", "    'total_amount': 'mean',\n", "    'avg_order_value': 'mean',\n", "    'total_orders': 'mean',\n", "    'purchase_frequency': 'mean',\n", "    'order_count': 'mean',\n", "    'montant_moyen': 'mean'\n", "}).round(2)\n", "\n", "print(cluster_analysis)\n", "\n", "# 5.2 Visualisation des distributions\n", "print(\"\\n📈 Visualisation des distributions par cluster...\")\n", "\n", "# Sélection des variables importantes pour la visualisation\n", "key_variables = ['recency', 'monetary', 'total_amount', 'avg_order_value']\n", "\n", "# Création des boxplots\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "axes = axes.ravel()\n", "\n", "for idx, var in enumerate(key_variables):\n", "    sns.boxplot(x='cluster', y=var, data=df_complete_clustered, ax=axes[idx])\n", "    axes[idx].set_title(f'Distribution de {var} par cluster')\n", "    axes[idx].set_xlabel('Cluster')\n", "    axes[idx].set_ylabel(var)\n", "\n", "plt.tight_layout()\n", "export_figure(fig, notebook_name=\"3\", export_number=3, base_name=\"cluster_distributions\")\n", "\n", "# 5.3 Détection des outliers\n", "print(\"\\n🔍 Analyse des outliers...\")\n", "\n", "# Calcul des z-scores\n", "z_scores = stats.zscore(X_scaled)\n", "outliers = (abs(z_scores) > 3).any(axis=1)\n", "outlier_percentage = (outliers.sum() / len(X_scaled)) * 100\n", "\n", "print(f\"Nombre d'outliers potentiels : {outliers.sum()} ({outlier_percentage:.1f}% des clients)\")\n", "\n", "# Distribution des outliers par cluster\n", "outlier_clusters = df_complete_clustered.loc[outliers, 'cluster'].value_counts()\n", "print(\"\\nDistribution des outliers par cluster :\")\n", "for cluster, count in outlier_clusters.items():\n", "    percentage = (count / len(df_complete_clustered[df_complete_clustered['cluster'] == cluster])) * 100\n", "    print(f\"Cluster {cluster}: {count} outliers ({percentage:.1f}% du cluster)\")\n", "\n", "# 5.4 Analyse des variables catégorielles\n", "print(\"\\n📊 Analyse des variables catégorielles par cluster...\")\n", "\n", "categorical_vars = ['customer_age_category']  # Seule variable catégorielle disponible\n", "\n", "for var in categorical_vars:\n", "    if var in df_complete_clustered.columns:\n", "        print(f\"\\nDistribution de {var} par cluster :\")\n", "        cross_tab = pd.crosstab(df_complete_clustered['cluster'], df_complete_clustered[var], normalize='index') * 100\n", "        print(cross_tab.round(1))\n", "\n", "# 5.5 Synthèse des clusters\n", "print(\"\\n📝 Synthèse des clusters :\")\n", "\n", "for cluster in df_complete_clustered['cluster'].unique():\n", "    cluster_data = df_complete_clustered[df_complete_clustered['cluster'] == cluster]\n", "    print(f\"\\nCluster {cluster} :\")\n", "    print(f\"Taille : {len(cluster_data)} clients ({len(cluster_data)/len(df_complete_clustered)*100:.1f}%)\")\n", "    print(\"Caractéristiques principales :\")\n", "    for var in key_variables:\n", "        mean_val = cluster_data[var].mean()\n", "        print(f\"- {var} : {mean_val:.2f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"\\n🔍 Analyse détaillée des clusters...\")\n", "\n", "# 5.1 Analyse des caractéristiques par cluster\n", "print(\"\\n📊 Caractéristiques moyennes par cluster :\")\n", "cluster_analysis = df_complete_clustered.groupby('cluster').agg({\n", "    'recency': 'mean',\n", "    'frequency': 'mean',\n", "    'monetary': 'mean',\n", "    'customer_lifespan_days': 'mean',\n", "    'days_since_first_order': 'mean',\n", "    'total_amount': 'mean',\n", "    'avg_order_value': 'mean',\n", "    'total_orders': 'mean',\n", "    'purchase_frequency': 'mean',\n", "    'order_count': 'mean',\n", "    'montant_moyen': 'mean'\n", "}).round(2)\n", "\n", "print(cluster_analysis)\n", "\n", "# 5.2 Visualisation des distributions\n", "print(\"\\n📈 Visualisation des distributions par cluster...\")\n", "\n", "# Sélection des variables importantes pour la visualisation\n", "key_variables = ['recency', 'monetary', 'total_amount', 'avg_order_value']\n", "\n", "# Création des boxplots\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "axes = axes.ravel()\n", "\n", "for idx, var in enumerate(key_variables):\n", "    sns.boxplot(x='cluster', y=var, data=df_complete_clustered, ax=axes[idx])\n", "    axes[idx].set_title(f'Distribution de {var} par cluster')\n", "    axes[idx].set_xlabel('Cluster')\n", "    axes[idx].set_ylabel(var)\n", "\n", "plt.tight_layout()\n", "export_figure(fig, notebook_name=\"3\", export_number=3, base_name=\"cluster_distributions\")\n", "\n", "# 5.3 Détection des outliers\n", "print(\"\\n🔍 Analyse des outliers...\")\n", "\n", "# Calcul des z-scores\n", "z_scores = stats.zscore(X_scaled)\n", "outliers = (abs(z_scores) > 3).any(axis=1)\n", "outlier_percentage = (outliers.sum() / len(X_scaled)) * 100\n", "\n", "print(f\"Nombre d'outliers potentiels : {outliers.sum()} ({outlier_percentage:.1f}% des clients)\")\n", "\n", "# Distribution des outliers par cluster\n", "outlier_clusters = df_complete_clustered.loc[outliers, 'cluster'].value_counts()\n", "print(\"\\nDistribution des outliers par cluster :\")\n", "for cluster, count in outlier_clusters.items():\n", "    percentage = (count / len(df_complete_clustered[df_complete_clustered['cluster'] == cluster])) * 100\n", "    print(f\"Cluster {cluster}: {count} outliers ({percentage:.1f}% du cluster)\")\n", "\n", "# 5.4 Analyse des variables catégorielles\n", "print(\"\\n📊 Analyse des variables catégorielles par cluster...\")\n", "\n", "categorical_vars = ['customer_age_category']  # Seule variable catégorielle disponible\n", "\n", "for var in categorical_vars:\n", "    if var in df_complete_clustered.columns:\n", "        print(f\"\\nDistribution de {var} par cluster :\")\n", "        cross_tab = pd.crosstab(df_complete_clustered['cluster'], df_complete_clustered[var], normalize='index') * 100\n", "        print(cross_tab.round(1))\n", "\n", "# 5.5 Synthèse des clusters\n", "print(\"\\n📝 Synthèse des clusters :\")\n", "\n", "for cluster in df_complete_clustered['cluster'].unique():\n", "    cluster_data = df_complete_clustered[df_complete_clustered['cluster'] == cluster]\n", "    print(f\"\\nCluster {cluster} :\")\n", "    print(f\"Taille : {len(cluster_data)} clients ({len(cluster_data)/len(df_complete_clustered)*100:.1f}%)\")\n", "    print(\"Caractéristiques principales :\")\n", "    for var in key_variables:\n", "        mean_val = cluster_data[var].mean()\n", "        print(f\"- {var} : {mean_val:.2f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"\\n🔍 Analyse détaillée des clusters...\")\n", "\n", "# 5.1 Analyse des caractéristiques par cluster\n", "print(\"\\n📊 Caractéristiques moyennes par cluster :\")\n", "cluster_analysis = df_complete_clustered.groupby('cluster').agg({\n", "    'recency': 'mean',\n", "    'frequency': 'mean',\n", "    'monetary': 'mean',\n", "    'customer_lifespan_days': 'mean',\n", "    'days_since_first_order': 'mean',\n", "    'total_amount': 'mean',\n", "    'avg_order_value': 'mean',\n", "    'total_orders': 'mean',\n", "    'purchase_frequency': 'mean',\n", "    'order_count': 'mean',\n", "    'montant_moyen': 'mean'\n", "}).round(2)\n", "\n", "print(cluster_analysis)\n", "\n", "# 5.2 Visualisation des distributions\n", "print(\"\\n📈 Visualisation des distributions par cluster...\")\n", "\n", "# Sélection des variables importantes pour la visualisation\n", "key_variables = ['recency', 'monetary', 'total_amount', 'avg_order_value']\n", "\n", "# Création des boxplots\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "axes = axes.ravel()\n", "\n", "for idx, var in enumerate(key_variables):\n", "    sns.boxplot(x='cluster', y=var, data=df_complete_clustered, ax=axes[idx])\n", "    axes[idx].set_title(f'Distribution de {var} par cluster')\n", "    axes[idx].set_xlabel('Cluster')\n", "    axes[idx].set_ylabel(var)\n", "\n", "plt.tight_layout()\n", "export_figure(fig, notebook_name=\"3\", export_number=3, base_name=\"cluster_distributions\")\n", "\n", "# 5.3 Détection des outliers\n", "print(\"\\n🔍 Analyse des outliers...\")\n", "\n", "# Calcul des z-scores\n", "z_scores = stats.zscore(X_scaled)\n", "outliers = (abs(z_scores) > 3).any(axis=1)\n", "outlier_percentage = (outliers.sum() / len(X_scaled)) * 100\n", "\n", "print(f\"Nombre d'outliers potentiels : {outliers.sum()} ({outlier_percentage:.1f}% des clients)\")\n", "\n", "# Distribution des outliers par cluster\n", "outlier_clusters = df_complete_clustered.loc[outliers, 'cluster'].value_counts()\n", "print(\"\\nDistribution des outliers par cluster :\")\n", "for cluster, count in outlier_clusters.items():\n", "    percentage = (count / len(df_complete_clustered[df_complete_clustered['cluster'] == cluster])) * 100\n", "    print(f\"Cluster {cluster}: {count} outliers ({percentage:.1f}% du cluster)\")\n", "\n", "# 5.4 Analyse des variables catégorielles\n", "print(\"\\n📊 Analyse des variables catégorielles par cluster...\")\n", "\n", "categorical_vars = ['customer_age_category']  # Seule variable catégorielle disponible\n", "\n", "for var in categorical_vars:\n", "    if var in df_complete_clustered.columns:\n", "        print(f\"\\nDistribution de {var} par cluster :\")\n", "        cross_tab = pd.crosstab(df_complete_clustered['cluster'], df_complete_clustered[var], normalize='index') * 100\n", "        print(cross_tab.round(1))\n", "\n", "# 5.5 Synthèse des clusters\n", "print(\"\\n📝 Synthèse des clusters :\")\n", "\n", "for cluster in df_complete_clustered['cluster'].unique():\n", "    cluster_data = df_complete_clustered[df_complete_clustered['cluster'] == cluster]\n", "    print(f\"\\nCluster {cluster} :\")\n", "    print(f\"Taille : {len(cluster_data)} clients ({len(cluster_data)/len(df_complete_clustered)*100:.1f}%)\")\n", "    print(\"Caractéristiques principales :\")\n", "    for var in key_variables:\n", "        mean_val = cluster_data[var].mean()\n", "        print(f\"- {var} : {mean_val:.2f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"\\n🔍 Analyse détaillée des clusters...\")\n", "\n", "# 5.1 Analyse des caractéristiques par cluster\n", "print(\"\\n📊 Caractéristiques moyennes par cluster :\")\n", "cluster_analysis = df_complete_clustered.groupby('cluster').agg({\n", "    'recency': 'mean',\n", "    'frequency': 'mean',\n", "    'monetary': 'mean',\n", "    'customer_lifespan_days': 'mean',\n", "    'days_since_first_order': 'mean',\n", "    'total_amount': 'mean',\n", "    'avg_order_value': 'mean',\n", "    'total_orders': 'mean',\n", "    'purchase_frequency': 'mean',\n", "    'order_count': 'mean',\n", "    'montant_moyen': 'mean'\n", "}).round(2)\n", "\n", "print(cluster_analysis)\n", "\n", "# 5.2 Visualisation des distributions\n", "print(\"\\n📈 Visualisation des distributions par cluster...\")\n", "\n", "# Sélection des variables importantes pour la visualisation\n", "key_variables = ['recency', 'monetary', 'total_amount', 'avg_order_value']\n", "\n", "# Création des boxplots\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "axes = axes.ravel()\n", "\n", "for idx, var in enumerate(key_variables):\n", "    sns.boxplot(x='cluster', y=var, data=df_complete_clustered, ax=axes[idx])\n", "    axes[idx].set_title(f'Distribution de {var} par cluster')\n", "    axes[idx].set_xlabel('Cluster')\n", "    axes[idx].set_ylabel(var)\n", "\n", "plt.tight_layout()\n", "export_figure(fig, notebook_name=\"3\", export_number=3, base_name=\"cluster_distributions\")\n", "\n", "# 5.3 Détection des outliers\n", "print(\"\\n🔍 Analyse des outliers...\")\n", "\n", "# Calcul des z-scores\n", "z_scores = stats.zscore(X_scaled)\n", "outliers = (abs(z_scores) > 3).any(axis=1)\n", "outlier_percentage = (outliers.sum() / len(X_scaled)) * 100\n", "\n", "print(f\"Nombre d'outliers potentiels : {outliers.sum()} ({outlier_percentage:.1f}% des clients)\")\n", "\n", "# Distribution des outliers par cluster\n", "outlier_clusters = df_complete_clustered.loc[outliers, 'cluster'].value_counts()\n", "print(\"\\nDistribution des outliers par cluster :\")\n", "for cluster, count in outlier_clusters.items():\n", "    percentage = (count / len(df_complete_clustered[df_complete_clustered['cluster'] == cluster])) * 100\n", "    print(f\"Cluster {cluster}: {count} outliers ({percentage:.1f}% du cluster)\")\n", "\n", "# 5.4 Analyse des variables catégorielles\n", "print(\"\\n📊 Analyse des variables catégorielles par cluster...\")\n", "\n", "categorical_vars = ['customer_age_category']  # Seule variable catégorielle disponible\n", "\n", "for var in categorical_vars:\n", "    if var in df_complete_clustered.columns:\n", "        print(f\"\\nDistribution de {var} par cluster :\")\n", "        cross_tab = pd.crosstab(df_complete_clustered['cluster'], df_complete_clustered[var], normalize='index') * 100\n", "        print(cross_tab.round(1))\n", "\n", "# 5.5 Synthèse des clusters\n", "print(\"\\n📝 Synthèse des clusters :\")\n", "\n", "for cluster in df_complete_clustered['cluster'].unique():\n", "    cluster_data = df_complete_clustered[df_complete_clustered['cluster'] == cluster]\n", "    print(f\"\\nCluster {cluster} :\")\n", "    print(f\"Taille : {len(cluster_data)} clients ({len(cluster_data)/len(df_complete_clustered)*100:.1f}%)\")\n", "    print(\"Caractéristiques principales :\")\n", "    for var in key_variables:\n", "        mean_val = cluster_data[var].mean()\n", "        print(f\"- {var} : {mean_val:.2f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"\\n🔍 Analyse détaillée des clusters...\")\n", "\n", "# 5.1 Analyse des caractéristiques par cluster\n", "print(\"\\n📊 Caractéristiques moyennes par cluster :\")\n", "cluster_analysis = df_complete_clustered.groupby('cluster').agg({\n", "    'recency': 'mean',\n", "    'frequency': 'mean',\n", "    'monetary': 'mean',\n", "    'customer_lifespan_days': 'mean',\n", "    'days_since_first_order': 'mean',\n", "    'total_amount': 'mean',\n", "    'avg_order_value': 'mean',\n", "    'total_orders': 'mean',\n", "    'purchase_frequency': 'mean',\n", "    'order_count': 'mean',\n", "    'montant_moyen': 'mean'\n", "}).round(2)\n", "\n", "print(cluster_analysis)\n", "\n", "# 5.2 Visualisation des distributions\n", "print(\"\\n📈 Visualisation des distributions par cluster...\")\n", "\n", "# Sélection des variables importantes pour la visualisation\n", "key_variables = ['recency', 'monetary', 'total_amount', 'avg_order_value']\n", "\n", "# Création des boxplots\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "axes = axes.ravel()\n", "\n", "for idx, var in enumerate(key_variables):\n", "    sns.boxplot(x='cluster', y=var, data=df_complete_clustered, ax=axes[idx])\n", "    axes[idx].set_title(f'Distribution de {var} par cluster')\n", "    axes[idx].set_xlabel('Cluster')\n", "    axes[idx].set_ylabel(var)\n", "\n", "plt.tight_layout()\n", "export_figure(fig, notebook_name=\"3\", export_number=3, base_name=\"cluster_distributions\")\n", "\n", "# 5.3 Détection des outliers\n", "print(\"\\n🔍 Analyse des outliers...\")\n", "\n", "# Calcul des z-scores\n", "z_scores = stats.zscore(X_scaled)\n", "outliers = (abs(z_scores) > 3).any(axis=1)\n", "outlier_percentage = (outliers.sum() / len(X_scaled)) * 100\n", "\n", "print(f\"Nombre d'outliers potentiels : {outliers.sum()} ({outlier_percentage:.1f}% des clients)\")\n", "\n", "# Distribution des outliers par cluster\n", "outlier_clusters = df_complete_clustered.loc[outliers, 'cluster'].value_counts()\n", "print(\"\\nDistribution des outliers par cluster :\")\n", "for cluster, count in outlier_clusters.items():\n", "    percentage = (count / len(df_complete_clustered[df_complete_clustered['cluster'] == cluster])) * 100\n", "    print(f\"Cluster {cluster}: {count} outliers ({percentage:.1f}% du cluster)\")\n", "\n", "# 5.4 Analyse des variables catégorielles\n", "print(\"\\n📊 Analyse des variables catégorielles par cluster...\")\n", "\n", "categorical_vars = ['customer_age_category']  # Seule variable catégorielle disponible\n", "\n", "for var in categorical_vars:\n", "    if var in df_complete_clustered.columns:\n", "        print(f\"\\nDistribution de {var} par cluster :\")\n", "        cross_tab = pd.crosstab(df_complete_clustered['cluster'], df_complete_clustered[var], normalize='index') * 100\n", "        print(cross_tab.round(1))\n", "\n", "# 5.5 Synthèse des clusters\n", "print(\"\\n📝 Synthèse des clusters :\")\n", "\n", "for cluster in df_complete_clustered['cluster'].unique():\n", "    cluster_data = df_complete_clustered[df_complete_clustered['cluster'] == cluster]\n", "    print(f\"\\nCluster {cluster} :\")\n", "    print(f\"Taille : {len(cluster_data)} clients ({len(cluster_data)/len(df_complete_clustered)*100:.1f}%)\")\n", "    print(\"Caractéristiques principales :\")\n", "    for var in key_variables:\n", "        mean_val = cluster_data[var].mean()\n", "        print(f\"- {var} : {mean_val:.2f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. <PERSON><PERSON><PERSON> <PERSON><PERSON>\n", "\n", "### 3.1 Entraînement de KMeans avec k optimal"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Entraînement du modèle K-Means final\n", "print(f\"\\n🚀 Entraînement du modèle K-Means final avec K = {optimal_k}...\")\n", "\n", "# Entraînement avec le k optimal et paramètres optimisés\n", "kmeans_final = KMeans(\n", "    n_clusters=optimal_k,\n", "    random_state=SEED,\n", "    n_init=10,  # Réduit de 20 à 10\n", "    max_iter=300,  # Réduit de 500 à 300\n", "    tol=1e-4,  # Augmenté de 1e-6 à 1e-4\n", "    algorithm='elkan'  # Plus rapide que 'auto'\n", ")\n", "\n", "cluster_labels = kmeans_final.fit_predict(X_scaled)\n", "\n", "# Ajout des labels aux datasets\n", "X_with_clusters = X_scaled.copy()\n", "X_with_clusters['cluster'] = cluster_labels\n", "\n", "X_with_ids_clustered = X_with_ids.copy()\n", "X_with_ids_clustered['cluster'] = cluster_labels\n", "\n", "df_complete_clustered = df_complete.copy()\n", "df_complete_clustered['cluster'] = cluster_labels\n", "\n", "# Métriques finales - calcul sur un échantillon pour la silhouette\n", "sample_size = min(10000, len(X_scaled))\n", "X_sample = X_scaled.sample(n=sample_size, random_state=SEED)\n", "sample_labels = kmeans_final.predict(X_sample)\n", "\n", "final_silhouette = silhouette_score(X_sample, sample_labels)\n", "final_calinski = calinski_harabasz_score(X_scaled, cluster_labels)\n", "final_inertia = kmeans_final.inertia_\n", "\n", "print(f\"\\n✅ Clustering réalisé avec K = {optimal_k}\")\n", "print(f\"📊 Métriques finales :\")\n", "print(f\"   Score de silhouette : {final_silhouette:.3f} (calculé sur {sample_size} points)\")\n", "print(f\"   Score de Calinski-Harabasz : {final_calinski:.1f}\")\n", "print(f\"   Inertie : {final_inertia:.1f}\")\n", "\n", "# Distribution des clusters\n", "cluster_counts = pd.Series(cluster_labels).value_counts().sort_index()\n", "print(f\"\\n📈 Distribution des clusters :\")\n", "for cluster_id, count in cluster_counts.items():\n", "    percentage = (count / len(cluster_labels)) * 100\n", "    print(f\"   Cluster {cluster_id}: {count:,} clients ({percentage:.1f}%)\")\n", "\n", "# Vérification de l'équilibre des clusters\n", "min_cluster_size = cluster_counts.min()\n", "max_cluster_size = cluster_counts.max()\n", "balance_ratio = min_cluster_size / max_cluster_size\n", "\n", "print(f\"\\n🔍 Équilibre des clusters :\")\n", "print(f\"   Ratio min/max : {balance_ratio:.2f}\")\n", "if balance_ratio >= 0.3:\n", "    print(\"   ✅ Clusters bien équilibrés\")\n", "elif balance_ratio >= 0.1:\n", "    print(\"   ⚠️ Clusters moyennement équilibrés\")\n", "else:\n", "    print(\"   ❌ Clusters déséquilibrés - à surveiller\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.2 Analyse des centres de clusters"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyse des centres de clusters\n", "print(\"\\n🔍 Analyse des centres de clusters...\")\n", "\n", "# Récupération des centres\n", "cluster_centers = kmeans_final.cluster_centers_\n", "centers_df = pd.DataFrame(cluster_centers, columns=X_scaled.columns)\n", "centers_df.index = [f'Cluster_{i}' for i in range(optimal_k)]\n", "\n", "print(\"\\n📊 Centres des clusters (valeurs normalisées) :\")\n", "display(centers_df.round(3))\n", "\n", "# Visualisation des centres avec heatmap\n", "fig, axes = plt.subplots(1, 2, figsize=(20, 8))\n", "\n", "# Heatmap des centres\n", "sns.heatmap(centers_df.T, annot=True, cmap='RdBu_r', center=0,\n", "            fmt='.2f', cbar_kws={'label': 'Valeur normalisée'},\n", "            ax=axes[0])\n", "axes[0].set_title('Heatmap des centres de clusters\\n(Valeurs normalisées)')\n", "axes[0].set_xlabel('Clusters')\n", "axes[0].set_ylabel('Variables')\n", "\n", "# Graphique radar des profils (si pas trop de variables)\n", "if len(X_scaled.columns) <= 10:\n", "    # Utilisation du module de visualisation optimisé\n", "    fig_radar = plot_cluster_profiles(centers_df, optimal_k)\n", "    axes[1].remove()  # Supprimer le subplot pour le remplacer\n", "    # Note: le graphique radar sera affiché séparément\n", "else:\n", "    # Graphique en barres pour les moyennes\n", "    centers_df.T.plot(kind='bar', ax=axes[1], width=0.8)\n", "    axes[1].set_title('Profils des clusters (barres)')\n", "    axes[1].set_xlabel('Variables')\n", "    axes[1].set_ylabel('Valeur normalisée')\n", "    axes[1].legend(title='Clusters', bbox_to_anchor=(1.05, 1), loc='upper left')\n", "    axes[1].tick_params(axis='x', rotation=45)\n", "\n", "plt.tight_layout()\n", "export_figure(plt.gcf(), notebook_name=\"3\", export_number=3, base_name=\"cluster_centers\")\n", "plt.show()\n", "\n", "# Identification des caractéristiques principales par cluster\n", "print(\"\\n🎯 Caractéristiques principales par cluster :\")\n", "for i, cluster_name in enumerate(centers_df.index):\n", "    center = centers_df.iloc[i]\n", "    high_features = center[center > 0.5].sort_values(ascending=False)\n", "    low_features = center[center < -0.5].sort_values()\n", "    moderate_high = center[(center > 0.2) & (center <= 0.5)].sort_values(ascending=False)\n", "    moderate_low = center[(center < -0.2) & (center >= -0.5)].sort_values()\n", "\n", "    print(f\"\\n📋 {cluster_name} ({cluster_counts[i]:,} clients):\")\n", "    if len(high_features) > 0:\n", "        print(f\"   🔴 Très élevé: {', '.join(high_features.index[:3])}\")\n", "    if len(moderate_high) > 0:\n", "        print(f\"   🟠 Élevé: {', '.join(moderate_high.index[:3])}\")\n", "    if len(moderate_low) > 0:\n", "        print(f\"   🟡 Faible: {', '.join(moderate_low.index[:3])}\")\n", "    if len(low_features) > 0:\n", "        print(f\"   🔵 Très faible: {', '.join(low_features.index[:3])}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.3 Sauvegarde du modèle entraîné (optionnel)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Sauvegarde du modèle entraîné\n", "print(\"\\n💾 Sauvegarde du modèle et des métadonnées...\")\n", "\n", "# Création du dossier models s'il n'existe pas\n", "os.makedirs('reports/models', exist_ok=True)\n", "\n", "# Sauvegarde du modèle\n", "model_path = 'reports/models/3_01_kmeans_segmentation.pkl'\n", "joblib.dump(kmeans_final, model_path)\n", "print(f\"✅ Modèle sauvegard<PERSON> : {model_path}\")\n", "\n", "# Sauvegarde des métadonnées du modèle\n", "model_metadata = {\n", "    'model_type': 'KMeans',\n", "    'n_clusters': optimal_k,\n", "    'features_used': list(X_scaled.columns),\n", "    'n_samples': len(X_scaled),\n", "    'silhouette_score': final_silhouette,\n", "    'calinski_harabasz_score': final_calinski,\n", "    'inertia': final_inertia,\n", "    'random_state': SEED,\n", "    'training_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),\n", "    'cluster_distribution': cluster_counts.to_dict(),\n", "    'optimization_results': {\n", "        'k_range_tested': list(k_range),\n", "        'optimal_k_silhouette': optimal_k_silhouette,\n", "        'optimal_k_calinski': optimal_k_calinski,\n", "        'justification': justification\n", "    }\n", "}\n", "\n", "metadata_path = 'reports/models/3_01_kmeans_metadata.json'\n", "with open(metadata_path, 'w') as f:\n", "    json.dump(model_metadata, f, indent=2, default=str)\n", "\n", "print(f\"✅ Métadonnées sauvegardées : {metadata_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Réduction de dimension pour visualisation\n", "\n", "### 4.1 Application de PCA (2D et 3D)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Réduction de dimension avec PCA\n", "print(\"\\n🔍 Application de l'ACP pour la visualisation...\")\n", "\n", "# PCA 2D pour visualisation\n", "pca_2d = PCA(n_components=2, random_state=SEED)\n", "X_pca_2d = pca_2d.fit_transform(X_scaled)\n", "\n", "# PCA 3D pour visualisation avancée\n", "pca_3d = PCA(n_components=3, random_state=SEED)\n", "X_pca_3d = pca_3d.fit_transform(X_scaled)\n", "\n", "# Variance expliquée\n", "variance_2d = pca_2d.explained_variance_ratio_.sum()\n", "variance_3d = pca_3d.explained_variance_ratio_.sum()\n", "\n", "print(f\"📊 Variance expliquée :\")\n", "print(f\"   PCA 2D : {variance_2d:.1%} (PC1: {pca_2d.explained_variance_ratio_[0]:.1%}, PC2: {pca_2d.explained_variance_ratio_[1]:.1%})\")\n", "print(f\"   PCA 3D : {variance_3d:.1%} (PC3: {pca_3d.explained_variance_ratio_[2]:.1%})\")\n", "\n", "# Évaluation de la qualité de la réduction\n", "if variance_2d >= 0.6:\n", "    print(\"   ✅ Excellente représentation en 2D\")\n", "elif variance_2d >= 0.4:\n", "    print(\"   ⚠️ Représentation correcte en 2D\")\n", "else:\n", "    print(\"   ❌ Représentation limitée en 2D - considérer plus de dimensions\")\n", "\n", "# Création des DataFrames pour visualisation\n", "pca_2d_df = pd.DataFrame({\n", "    'PC1': X_pca_2d[:, 0],\n", "    'PC2': X_pca_2d[:, 1],\n", "    'cluster': cluster_labels\n", "})\n", "\n", "pca_3d_df = pd.DataFrame({\n", "    'PC1': X_pca_3d[:, 0],\n", "    'PC2': X_pca_3d[:, 1],\n", "    'PC3': X_pca_3d[:, 2],\n", "    'cluster': cluster_labels\n", "})\n", "\n", "# Analyse des contributions des variables aux composantes principales\n", "print(\"\\n🔍 Contributions principales aux composantes :\")\n", "components_df = pd.DataFrame(\n", "    pca_2d.components_.T,\n", "    columns=['PC1', 'PC2'],\n", "    index=X_scaled.columns\n", ")\n", "\n", "# Variables les plus contributives pour PC1 et PC2\n", "pc1_contrib = components_df['PC1'].abs().sort_values(ascending=False)\n", "pc2_contrib = components_df['PC2'].abs().sort_values(ascending=False)\n", "\n", "print(f\"   PC1 ({pca_2d.explained_variance_ratio_[0]:.1%}): {', '.join(pc1_contrib.head(3).index)}\")\n", "print(f\"   PC2 ({pca_2d.explained_variance_ratio_[1]:.1%}): {', '.join(pc2_contrib.head(3).index)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.2 Visualisation des clusters (scatterplot coloré)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualisation 2D des clusters\n", "print(\"\\n📊 Création des visualisations 2D des clusters...\")\n", "\n", "export_figure(fig, notebook_name=\"3\", export_number=4, base_name=\"clusters_2d_pca\")\n", "\n", "# Visualisation alternative avec seaborn pour plus de détails\n", "fig, axes = plt.subplots(1, 2, figsize=(16, 6))\n", "\n", "# Scatterplot avec seaborn\n", "sns.scatterplot(\n", "    data=pca_2d_df,\n", "    x='PC1', y='PC2',\n", "    hue='cluster',\n", "    palette='Set2',\n", "    alpha=0.7,\n", "    s=60,\n", "    ax=axes[0]\n", ")\n", "axes[0].set_xlabel(f'PC1 ({pca_2d.explained_variance_ratio_[0]:.1%} de variance)')\n", "axes[0].set_ylabel(f'PC2 ({pca_2d.explained_variance_ratio_[1]:.1%} de variance)')\n", "axes[0].set_title('Clusters en 2D (PCA) - <PERSON>ue détaillée')\n", "axes[0].grid(True, alpha=0.3)\n", "\n", "# Ajout des centres\n", "centers_pca = pca_2d.transform(cluster_centers)\n", "axes[0].scatter(centers_pca[:, 0], centers_pca[:, 1],\n", "               c='red', marker='X', s=200, linewidth=2,\n", "               label='Centres', edgecolors='black')\n", "axes[0].legend()\n", "\n", "# Graphique de densité\n", "for i in range(optimal_k):\n", "    mask = cluster_labels == i\n", "    axes[1].scatter(X_pca_2d[mask, 0], X_pca_2d[mask, 1],\n", "                   alpha=0.3, s=30, label=f'Cluster {i}')\n", "\n", "# Ellipses de confiance (optionnel)\n", "from matplotlib.patches import Ellipse\n", "for i in range(optimal_k):\n", "    mask = cluster_labels == i\n", "    if np.sum(mask) > 1:  # Au moins 2 points pour calculer la covariance\n", "        cluster_data = X_pca_2d[mask]\n", "        mean = cluster_data.mean(axis=0)\n", "        cov = np.cov(cluster_data.T)\n", "\n", "        # Calcul des axes de l'ellipse\n", "        eigenvals, eigenvecs = np.linalg.eigh(cov)\n", "        angle = np.degrees(np.arctan2(eigenvecs[1, 0], eigenvecs[0, 0]))\n", "        width, height = 2 * np.sqrt(eigenvals)\n", "\n", "        ellipse = Ellipse(mean, width, height, angle=angle,\n", "                         alpha=0.2, facecolor=plt.cm.Set2(i/optimal_k))\n", "        axes[1].add_patch(ellipse)\n", "\n", "axes[1].set_xlabel(f'PC1 ({pca_2d.explained_variance_ratio_[0]:.1%} de variance)')\n", "axes[1].set_ylabel(f'PC2 ({pca_2d.explained_variance_ratio_[1]:.1%} de variance)')\n", "axes[1].set_title('Clusters avec ellipses de confiance')\n", "axes[1].grid(True, alpha=0.3)\n", "axes[1].legend()\n", "\n", "plt.tight_layout()\n", "export_figure(plt.gcf(), notebook_name=\"3\", export_number=5, base_name=\"clusters_detailed\")\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.3 Visualisation 3D interactive (optionnel)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualisation 3D des clusters\n", "print(\"\\n📊 Création de la visualisation 3D des clusters...\")\n", "\n", "# Graphique 3D avec matplotlib\n", "from mpl_toolkits.mplot3d import Axes3D\n", "\n", "fig = plt.figure(figsize=(14, 10))\n", "ax = fig.add_subplot(111, projection='3d')\n", "\n", "# Pa<PERSON> de couleurs\n", "colors = plt.cm.tab10(np.linspace(0, 1, optimal_k))\n", "\n", "# Affichage des points par cluster\n", "for i in range(optimal_k):\n", "    mask = cluster_labels == i\n", "    ax.scatter(X_pca_3d[mask, 0], X_pca_3d[mask, 1], X_pca_3d[mask, 2],\n", "              c=[colors[i]], label=f'Cluster {i}', alpha=0.6, s=50)\n", "\n", "# Centres en 3D\n", "centers_pca_3d = pca_3d.transform(cluster_centers)\n", "ax.scatter(centers_pca_3d[:, 0], centers_pca_3d[:, 1], centers_pca_3d[:, 2],\n", "          c='red', marker='X', s=300, linewidth=2,\n", "          label='Centres', edgecolors='black')\n", "\n", "# Labels et titre\n", "ax.set_xlabel(f'PC1 ({pca_3d.explained_variance_ratio_[0]:.1%})')\n", "ax.set_ylabel(f'PC2 ({pca_3d.explained_variance_ratio_[1]:.1%})')\n", "ax.set_zlabel(f'PC3 ({pca_3d.explained_variance_ratio_[2]:.1%})')\n", "ax.set_title(f'Visualisation 3D des clusters (PCA)\\nVariance expliquée: {variance_3d:.1%}')\n", "\n", "# Légende\n", "ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')\n", "\n", "# Amélioration de l'affichage\n", "ax.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "export_figure(plt.gcf(), notebook_name=\"3\", export_number=6, base_name=\"clusters_3d_pca\")\n", "plt.show()\n", "\n", "# Statistiques sur la séparation 3D\n", "print(f\"\\n📊 Qualité de la visualisation 3D :\")\n", "print(f\"   Variance expliquée totale : {variance_3d:.1%}\")\n", "print(f\"   Gain par rapport à 2D : {variance_3d - variance_2d:.1%}\")\n", "\n", "if variance_3d - variance_2d > 0.1:\n", "    print(\"   ✅ La 3D apporte une information significative\")\n", "else:\n", "    print(\"   ⚠️ La 3D apporte peu d'information supplémentaire\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. <PERSON><PERSON><PERSON>\n", "\n", "### 5.1 Agrégation des indicateurs par cluster"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyse descriptive par cluster (données originales)\n", "print(\"\\n📊 Analyse descriptive détaillée par cluster...\")\n", "\n", "# Sélection des variables clés pour l'analyse\n", "key_variables = [\n", "    'recency', 'frequency', 'monetary_total', 'monetary_avg',\n", "    'customer_lifespan_days', 'days_since_first_order',\n", "    'avg_days_between_orders'\n", "]\n", "\n", "# Vérification de la présence des variables\n", "available_vars = [var for var in key_variables if var in df_complete_clustered.columns]\n", "print(f\"Variables disponibles pour l'analyse : {available_vars}\")\n", "\n", "# Agrégation sur les données originales (non normalisées)\n", "agg_dict = {}\n", "for var in available_vars:\n", "    agg_dict[var] = ['mean', 'median', 'std', 'min', 'max']\n", "\n", "# Ajout du comptage\n", "if 'customer_id' in df_complete_clustered.columns:\n", "    agg_dict['customer_id'] = 'count'\n", "else:\n", "    # Utiliser l'index pour compter\n", "    df_complete_clustered['_count'] = 1\n", "    agg_dict['_count'] = 'sum'\n", "\n", "cluster_analysis = df_complete_clustered.groupby('cluster').agg(agg_dict).round(2)\n", "\n", "# Aplatir les colonnes multi-index\n", "cluster_analysis.columns = ['_'.join(col).strip() for col in cluster_analysis.columns]\n", "\n", "# Renommer la colonne de comptage\n", "count_col = 'customer_id_count' if 'customer_id_count' in cluster_analysis.columns else '_count_sum'\n", "if count_col in cluster_analysis.columns:\n", "    cluster_analysis = cluster_analysis.rename(columns={count_col: 'cluster_size'})\n", "\n", "print(\"\\n📋 Analyse descriptive par cluster (valeurs originales) :\")\n", "display(cluster_analysis)\n", "\n", "# Sauvegarde de l'analyse détaillée\n", "cluster_analysis.to_csv('reports/analysis/3_02_cluster_analysis_detailed.csv')\n", "print(f\"\\n💾 Analyse détaillée sauvegardée : reports/analysis/3_02_cluster_analysis_detailed.csv\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5.2 Tableaux de synthèse et graphiques comparatifs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Tableaux de synthèse des profils\n", "print(\"\\n📊 Création du tableau de synthèse des segments...\")\n", "\n", "# Identification des colonnes de moyennes disponibles\n", "mean_columns = [col for col in cluster_analysis.columns if col.endswith('_mean')]\n", "print(f\"Colonnes de moyennes disponibles : {mean_columns}\")\n", "\n", "# Création du tableau simplifié\n", "summary_columns = []\n", "summary_labels = []\n", "\n", "# Mapping des colonnes vers des noms lisibles\n", "column_mapping = {\n", "    'recency_mean': 'Récence_moy',\n", "    'frequency_mean': 'Fréquence_moy',\n", "    'monetary_total_mean': 'Montant_total_moy',\n", "    'monetary_avg_mean': '<PERSON><PERSON>_moy_moy',\n", "    'customer_lifespan_days_mean': 'Ancienneté_moy'\n", "}\n", "\n", "for col, label in column_mapping.items():\n", "    if col in cluster_analysis.columns:\n", "        summary_columns.append(col)\n", "        summary_labels.append(label)\n", "\n", "# Ajout de la taille des clusters\n", "if 'cluster_size' in cluster_analysis.columns:\n", "    summary_columns.append('cluster_size')\n", "    summary_labels.append('Taille_cluster')\n", "\n", "# Création du tableau de synthèse\n", "summary_table = cluster_analysis[summary_columns].copy()\n", "summary_table.columns = summary_labels\n", "\n", "# Ajout de pourcentages si on a la taille des clusters\n", "if 'Taille_cluster' in summary_table.columns:\n", "    summary_table['%_clients'] = (summary_table['<PERSON>lle_cluster'] / summary_table['Taille_cluster'].sum() * 100).round(1)\n", "\n", "print(\"\\n📋 Tableau de synthèse des segments :\")\n", "display(summary_table)\n", "\n", "# Sauvegarde du tableau de synthèse\n", "summary_table.to_csv('reports/analysis/3_03_cluster_summary.csv')\n", "print(f\"\\n💾 Tableau de synthèse sauvegardé : reports/analysis/3_03_cluster_summary.csv\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5.3 Identification des profils types"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Identification et nommage des profils clients\n", "print(\"\\n🎯 Identification automatique des profils clients...\")\n", "\n", "# Analyse automatique des profils\n", "profile_descriptions = {}\n", "\n", "for cluster_id in range(optimal_k):\n", "    cluster_data = summary_table.iloc[cluster_id]\n", "    print(f\"\\n--- Cluster {cluster_id} ---\")\n", "    print(cluster_data)  # Debug : affiche toutes les valeurs\n", "\n", "    metrics = {}\n", "    if 'Récence_moy' in cluster_data.index:\n", "        metrics['recency'] = cluster_data['Récence_moy']\n", "    if 'Fréquence_moy' in cluster_data.index:\n", "        metrics['frequency'] = cluster_data['Fréquence_moy']\n", "    if 'Montant_total_moy' in cluster_data.index:\n", "        metrics['monetary'] = cluster_data['Montant_total_moy']\n", "    if 'Mont<PERSON>_moy_moy' in cluster_data.index:\n", "        metrics['avg_order'] = cluster_data['Montant_moy_moy']\n", "\n", "    # Classification plus souple\n", "    profile = \"Segment Indéterminé\"\n", "    r = metrics.get('recency', None)\n", "    f = metrics.get('frequency', None)\n", "    m = metrics.get('monetary', None)\n", "\n", "    if r is not None and f is not None and m is not None:\n", "        if r <= 50 and f >= 3 and m >= 200:\n", "            profile = \"🌟 Champions (Fidèles & Actifs)\"\n", "        elif r <= 100 and m >= 300:\n", "            profile = \"💎 Clients à Forte Valeur\"\n", "        elif f >= 4:\n", "            profile = \"🔄 Acheteurs Fréquents\"\n", "        elif r >= 200:\n", "            profile = \"😴 Clients Inactifs/Perdus\"\n", "        elif m <= 100:\n", "            profile = \"🛒 Petits Acheteurs\"\n", "        elif r <= 100:\n", "            profile = \"⭐ Clients Récents\"\n", "        else:\n", "            profile = \"📅 Clients Occasionnels\"\n", "    elif r is not None and f is not None:\n", "        if r >= 200:\n", "            profile = \"😴 Clients Inactifs/Perdus\"\n", "        elif f == 1:\n", "            profile = \"🛒 Acheteurs uniques\"\n", "        elif f >= 4:\n", "            profile = \"🔄 Acheteurs Fréquents\"\n", "        elif r <= 100:\n", "            profile = \"⭐ Clients Récents\"\n", "        else:\n", "            profile = \"📅 Clients Occasionnels\"\n", "    # Ajoutez d'autres cas selon les métriques disponibles\n", "\n", "    profile_descriptions[cluster_id] = profile\n", "\n", "# Affichage des profils identifiés\n", "print(\"\\n=== 🎯 PROFILS CLIENTS IDENTIFIÉS ===\")\n", "for cluster_id, profile in profile_descriptions.items():\n", "    cluster_data = summary_table.iloc[cluster_id]\n", "    print(f\"\\n📊 Cluster {cluster_id}: {profile}\")\n", "\n", "    if 'Taille_cluster' in cluster_data.index:\n", "        size = cluster_data['Taille_cluster']\n", "        pct = cluster_data.get('%_clients', 0)\n", "        print(f\"   👥 Taille: {size:.0f} clients ({pct:.1f}%)\")\n", "\n", "    # Affichage des métriques disponibles\n", "    for metric, label in [('Récence_moy', 'Récence'), ('Fréquence_moy', 'Fréquence'),\n", "                         ('<PERSON><PERSON>_total_moy', 'Valeur totale'), ('<PERSON><PERSON>_moy_moy', '<PERSON><PERSON> moyen')]:\n", "        if metric in cluster_data.index:\n", "            value = cluster_data[metric]\n", "            unit = ' jours' if 'Récence' in label else (' achats' if 'Fréquence' in label else '€')\n", "            print(f\"   📈 {label}: {value:.1f}{unit}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. <PERSON><PERSON><PERSON><PERSON>\n", "\n", "### 6.1 Export du DataFrame labellisé avec les clusters"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Sauvegarde des résultats de clustering\n", "print(\"\\n💾 Sauvegarde des résultats de clustering...\")\n", "\n", "# Création des dossiers nécessaires\n", "os.makedirs('data/processed', exist_ok=True)\n", "os.makedirs('reports/analysis', exist_ok=True)\n", "\n", "# Ajout des profils au dataset complet\n", "df_complete_clustered['cluster_profile'] = df_complete_clustered['cluster'].map(profile_descriptions)\n", "\n", "# Sauvegarde des datasets clusterisés\n", "df_complete_clustered.to_csv('data/processed/3_04_customers_clustered.csv', index=False)\n", "df_complete_clustered.to_pickle('data/processed/3_04_customers_clustered.pkl')\n", "\n", "# Dataset résumé pour analyse rapide\n", "base_columns = ['cluster', 'cluster_profile']\n", "available_columns = [col for col in ['customer_id', 'recency', 'frequency', 'monetary_total', 'monetary_avg']\n", "                    if col in df_complete_clustered.columns]\n", "\n", "summary_columns = base_columns + available_columns\n", "summary_for_export = df_complete_clustered[summary_columns].copy()\n", "\n", "summary_for_export.to_csv('data/processed/3_04_customer_segments_summary.csv', index=False)\n", "\n", "print(\"✅ Datasets sauvegardés :\")\n", "print(\"   - 3_04_customers_clustered.pkl : dataset complet avec clusters\")\n", "print(\"   - 3_04_customer_segments_summary.csv : résumé des segments\")\n", "print(f\"\\n📊 Nombre de clients segmentés : {len(df_complete_clustered):,}\")\n", "print(f\"📊 Nombre de segments créés : {optimal_k}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 6.2 Export des résumés et statistiques par segment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Export des analyses et statistiques\n", "print(\"\\n📊 Export des analyses et statistiques complètes...\")\n", "\n", "# Export des informations de clustering complètes\n", "clustering_info = {\n", "    'clustering_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),\n", "    'algorithm': 'K<PERSON><PERSON><PERSON>',\n", "    'n_clusters': optimal_k,\n", "    'silhouette_score': final_silhouette,\n", "    'calinski_harabasz_score': final_calinski,\n", "    'inertia': final_inertia,\n", "    'features_used': list(X_scaled.columns),\n", "    'cluster_profiles': profile_descriptions,\n", "    'optimization_process': {\n", "        'k_range_tested': list(k_range),\n", "        'inertias': inertias,\n", "        'silhouette_scores': silhouette_scores,\n", "        'calinski_scores': calinski_scores,\n", "        'optimal_k_silhouette': optimal_k_silhouette,\n", "        'optimal_k_calinski': optimal_k_calinski,\n", "        'selection_justification': justification\n", "    },\n", "    'pca_analysis': {\n", "        'variance_explained_2d': variance_2d,\n", "        'variance_explained_3d': variance_3d,\n", "        'components_2d': pca_2d.explained_variance_ratio_.tolist(),\n", "        'components_3d': pca_3d.explained_variance_ratio_.tolist()\n", "    },\n", "    'cluster_distribution': cluster_counts.to_dict(),\n", "    'balance_ratio': balance_ratio\n", "}\n", "\n", "# Ajout des statistiques du tableau de synthèse si disponible\n", "if 'Taille_cluster' in summary_table.columns:\n", "    clustering_info['cluster_sizes'] = summary_table['Taille_cluster'].to_dict()\n", "if '%_clients' in summary_table.columns:\n", "    clustering_info['cluster_percentages'] = summary_table['%_clients'].to_dict()\n", "\n", "# <PERSON>uve<PERSON><PERSON> du fichier JSON complet\n", "with open('reports/analysis/3_05_clustering_results_complete.json', 'w') as f:\n", "    json.dump(clustering_info, f, indent=2, default=str)\n", "\n", "print(\"✅ Analyses complètes sauvegardées :\")\n", "print(\"   - 3_02_cluster_analysis_detailed.csv : statistiques détaillées\")\n", "print(\"   - 3_03_cluster_summary.csv : tableau de synthèse\")\n", "print(\"   - 3_05_clustering_results_complete.json : informations complètes\")\n", "print(\"   - 3_01_kmeans_metadata.json : métadonnées du modèle\")\n", "\n", "print(f\"\\n🎯 Clustering terminé avec succès !\")\n", "print(f\"   📊 {optimal_k} segments identifiés\")\n", "print(f\"   📈 Score de silhouette : {final_silhouette:.3f}\")\n", "print(f\"   🎨 {len([f for f in os.listdir('reports/figures') if f.startswith('3_')])} visualisations générées\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Conclusion\n", "\n", "### ✅ Résumé des étapes réalisées\n", "\n", "- ✅ **Chargement des données** : Import des datasets préparés du Notebook 2\n", "- ✅ **Optimisation du nombre de clusters** : Méthodes du coude, silhouette et Calinski-Harabasz\n", "- ✅ **Clustering K-Means** : Entraînement avec paramètres optimisés\n", "- ✅ **Réduction de dimension** : PCA 2D et 3D pour visualisation\n", "- ✅ **Analyse des centres** : Caractérisation des profils par cluster\n", "- ✅ **Visualisations avancées** : Graphiques 2D/3D, heatmaps, ellipses de confiance\n", "- ✅ **Analyse descriptive** : Statistiques détaillées par segment\n", "- ✅ **Identification des profils** : Classification automatique des personas clients\n", "- ✅ **<PERSON><PERSON><PERSON><PERSON> complète** : <PERSON><PERSON><PERSON><PERSON>, donn<PERSON>, analyses et visualisations\n", "\n", "### 🎯 Livrables générés\n", "\n", "**📊 Données :**\n", "- `3_04_customers_clustered.pkl` : Dataset complet avec clusters\n", "- `3_04_customer_segments_summary.csv` : Résumé des segments\n", "\n", "**🤖 <PERSON><PERSON><PERSON><PERSON> :**\n", "- `3_01_kmeans_segmentation.pkl` : <PERSON><PERSON><PERSON><PERSON> entraîné\n", "- `3_01_kmeans_metadata.json` : Métadonnées du modèle\n", "\n", "**📈 Analyses :**\n", "- `3_02_cluster_analysis_detailed.csv` : Statistiques détaillées\n", "- `3_03_cluster_summary.csv` : Tableau de synthèse\n", "- `3_05_clustering_results_complete.json` : Résultats complets\n", "\n", "**🎨 Visualisations :**\n", "- `3_01_elbow_curve.png` : Courbe du coude et métriques\n", "- `3_02_clustering_metrics.png` : Comparaison des métriques\n", "- `3_03_cluster_centers.png` : Heatmap des centres\n", "- `3_04_clusters_2d_pca.png` : Clusters en 2D (PCA)\n", "- `3_05_clusters_detailed.png` : <PERSON><PERSON> détaillée avec ellipses\n", "- `3_06_clusters_3d_pca.png` : Clusters en 3D (PCA)\n", "\n", "### 🚀 Prochaines étapes\n", "\n", "➡️ **Notebook 4 :** Analyse détaillée des segments et recommandations marketing  \n", "➡️ **Notebook 5 :** Simulation de maintenance et contrat de service\n", "\n", "---\n", "\n", "**🎉 Segmentation client réalisée avec succès !**  \n", "**📊 Données prêtes pour l'analyse marketing et les recommandations business.**"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}