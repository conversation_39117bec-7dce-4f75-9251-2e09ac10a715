# Imports spécifiques pour ce notebook
import os
import json
from datetime import datetime
import joblib

# Clustering et métriques
from sklearn.cluster import KMeans, DBSCAN, AgglomerativeClustering
from sklearn.decomposition import PCA
from sklearn.metrics import silhouette_score, calinski_harabasz_score
from sklearn.preprocessing import StandardScaler
from itertools import combinations
from scipy.cluster.hierarchy import dendrogram, linkage
from scipy import stats

# Imports locaux - modules utils optimisés
from utils.core import (
    init_notebook, SEED, PROJECT_ROOT, REPORTS_DIR,
    # Les imports de base sont déjà dans core
    pd, np, plt, sns
)
from utils.clustering import (
    find_optimal_k,
    perform_kmeans,
    perform_dbscan,
    calculate_clustering_metrics,
    analyze_clusters,
    compare_clustering_algorithms
)
from utils.clustering_visualization import (
    plot_elbow_curve,
    plot_clusters_2d,
    plot_cluster_profiles,
    plot_cluster_feature_distributions,
    plot_cluster_sizes,
    export_figure
)
from utils.data_tools import load_data, export_artifact
from utils.save_load import save_results, load_results

# Configuration du notebook avec le module core
init_notebook(
    notebook_file_path="3_clustering_segmentation.ipynb",
    style="whitegrid",
    figsize=(12, 8),
    random_seed=SEED,
    setup=True,
    check_deps=True
)

print(f"\n📁 Répertoire de travail : {PROJECT_ROOT}")
print(f"📊 Répertoire des rapports : {REPORTS_DIR}")
print(f"🎲 Graine aléatoire : {SEED}")

# Chargement des données préparées du Notebook 2
print("🔄 Chargement des données préparées pour le clustering...")

# Chemins des fichiers générés par le notebook 2
data_path_scaled = 'data/processed/2_01_features_scaled_clustering.csv'
data_path_with_ids = 'data/processed/2_02_features_scaled_with_ids.csv'
data_path_complete = 'data/processed/2_03_rfm_enriched_complete.csv'

try:
    # Chargement des datasets
    X_scaled = pd.read_csv(data_path_scaled)
    X_with_ids = pd.read_csv(data_path_with_ids)
    df_complete = pd.read_csv(data_path_complete)

    print(f"✅ Dataset pour clustering : {X_scaled.shape}")
    print(f"   Variables : {list(X_scaled.columns)}")
    print(f"\n✅ Dataset avec IDs : {X_with_ids.shape}")
    print(f"✅ Dataset complet : {df_complete.shape}")

    # Vérification de la cohérence
    assert len(X_scaled) == len(X_with_ids) == len(df_complete), "Tailles incohérentes entre les datasets"
    print(f"\n✅ Cohérence vérifiée : {len(X_scaled)} clients dans tous les datasets")

except FileNotFoundError as e:
    print(f"❌ Erreur : Fichier non trouvé - {e}")
    print("💡 Assurez-vous d'avoir exécuté le Notebook 2 (Feature Engineering) avant ce notebook.")
    raise
except Exception as e:
    print(f"❌ Erreur lors du chargement : {e}")
    raise

# Vérification des variables "First Purchase" pour la segmentation
print("📊 Variables 'First Purchase' utilisées pour la segmentation :")

# Variables attendues selon la stratégie
expected_features = ['recency_days', 'order_value', 'state_encoded',
                    'purchase_month', 'delivery_days', 'review_score_filled']

print(f"\n🎯 Variables attendues selon la stratégie ({len(expected_features)}) :")
for i, feature in enumerate(expected_features, 1):
    status = "✅" if feature in X_scaled.columns else "❌"
    print(f"{i:2d}. {feature} {status}")

print(f"\n📋 Variables réellement disponibles ({len(X_scaled.columns)}) :")
for i, col in enumerate(X_scaled.columns, 1):
    expected = "(attendue)" if col in expected_features else "(non attendue)"
    print(f"{i:2d}. {col} {expected}")

# Vérification de la conformité à la stratégie
available_expected = [f for f in expected_features if f in X_scaled.columns]
missing_expected = [f for f in expected_features if f not in X_scaled.columns]
unexpected_features = [f for f in X_scaled.columns if f not in expected_features]

print(f"\n🔍 Analyse de conformité à la stratégie :")
print(f"   ✅ Variables attendues présentes : {len(available_expected)}/{len(expected_features)}")
if missing_expected:
    print(f"   ❌ Variables attendues manquantes : {missing_expected}")
if unexpected_features:
    print(f"   ⚠️ Variables non attendues : {unexpected_features}")

# Validation du nombre de variables (6 maximum selon stratégie)
if len(X_scaled.columns) <= 6:
    print(f"   ✅ Nombre de variables respecté : {len(X_scaled.columns)}/6 max")
else:
    print(f"   ⚠️ Trop de variables : {len(X_scaled.columns)}/6 max (stratégie non respectée)")

# Variables finales pour clustering (exclure les constantes)
variables_to_check = X_scaled.columns[X_scaled.std() > 0].tolist()
constant_variables = X_scaled.columns[X_scaled.std() == 0].tolist()

if constant_variables:
    print(f"\n⚠️ Variables constantes détectées (exclues du clustering) : {constant_variables}")

clustering_features = variables_to_check
print(f"\n🎯 Variables finales pour clustering ({len(clustering_features)}) : {clustering_features}")

# Vérification de la normalisation
if len(clustering_features) > 0:
    mean_max = X_scaled[clustering_features].mean().abs().max()
    std_mean = X_scaled[clustering_features].std().mean()
    print(f"\n🔍 Vérification normalisation : moyennes={mean_max:.6f}, écarts-types={std_mean:.6f}")

    if mean_max < 1e-10 and 0.9 <= std_mean <= 1.1:
        print("   ✅ Normalisation correcte")
    else:
        print("   ⚠️ Normalisation potentiellement incorrecte")
else:
    print("   ❌ Aucune variable valide pour clustering")

# Méthode du coude adaptée au contexte "First Purchase"
print("🔍 Recherche du nombre optimal de clusters pour segmentation 'First Purchase'...")
print(f"🎯 Objectif : 4-6 segments équilibrés avec score silhouette > 0.4")

# Vérification des données pour clustering
if 'clustering_features' not in locals() or len(clustering_features) == 0:
    print("❌ Aucune variable valide pour clustering détectée")
    print("💡 Utilisation de toutes les variables disponibles")
    clustering_features = X_scaled.columns.tolist()

# Dataset final pour clustering
X_clustering = X_scaled[clustering_features].copy()
print(f"\n📊 Dataset pour clustering : {X_clustering.shape}")
print(f"📋 Variables utilisées : {clustering_features}")

# Plage de k adaptée à la stratégie (4-6 segments attendus)
k_range = range(3, 8)  # 3 à 7 pour tester autour de 4-6
inertias = []
silhouette_scores = []

# Optimisation : utiliser un sous-ensemble pour le score de silhouette
sample_size = min(10000, len(X_clustering))  # Limiter à 10000 points max
if sample_size < len(X_clustering):
    print(f"📊 Utilisation d'un échantillon de {sample_size} points pour le score de silhouette")
    X_sample = X_clustering.sample(n=sample_size, random_state=SEED)
else:
    X_sample = X_clustering

print("\n🔄 Calcul en cours...")
for k in k_range:
    print(f"  K = {k}", end=" ")

    # K-Means avec paramètres optimisés
    kmeans = KMeans(
        n_clusters=k,
        random_state=SEED,
        n_init=10,  # Augmenté pour plus de stabilité
        max_iter=300,
        algorithm='lloyd'  # Plus stable pour les petits datasets
    )
    cluster_labels = kmeans.fit_predict(X_clustering)

    # Calcul du score de silhouette sur l'échantillon
    sample_labels = kmeans.predict(X_sample)
    silhouette_avg = silhouette_score(X_sample, sample_labels)

    inertias.append(kmeans.inertia_)
    silhouette_scores.append(silhouette_avg)

    # Indication si le score atteint l'objectif
    target_met = "🎯" if silhouette_avg > 0.4 else "📊"
    print(f"(Silhouette: {silhouette_avg:.3f} {target_met})")

print("\n✅ Calculs terminés")

# Identification des k qui atteignent l'objectif
target_k_values = [k for k, score in zip(k_range, silhouette_scores) if score > 0.4]
if target_k_values:
    print(f"\n🎯 Valeurs de K atteignant l'objectif (silhouette > 0.4) : {target_k_values}")
else:
    print(f"\n⚠️ Aucune valeur de K n'atteint l'objectif (silhouette > 0.4)")
    print(f"   Meilleur score obtenu : {max(silhouette_scores):.3f}")

# Utilisation du module de visualisation optimisé
fig = plot_elbow_curve(k_range, inertias, silhouette_scores)
export_figure(fig, notebook_name="3", export_number=1, base_name="elbow_curve_first_purchase")

# Affichage des résultats
print(f"\n📊 Inerties par k : {dict(zip(k_range, [round(i, 2) for i in inertias]))}")
print(f"📊 Scores silhouette : {dict(zip(k_range, [round(s, 3) for s in silhouette_scores]))}")

# Calcul des métriques complémentaires
print("\n🔍 Calcul des métriques complémentaires...")

calinski_scores = []
for k in k_range:
    # Recalcul pour obtenir les labels
    kmeans = KMeans(n_clusters=k, random_state=SEED, n_init=10, max_iter=300)
    cluster_labels = kmeans.fit_predict(X_clustering)

    # Score de Calinski-Harabasz
    calinski_score = calinski_harabasz_score(X_clustering, cluster_labels)
    calinski_scores.append(calinski_score)

# Identification des k optimaux par métrique
optimal_k_silhouette = k_range[np.argmax(silhouette_scores)]
optimal_k_calinski = k_range[np.argmax(calinski_scores)]

# Plage stratégique préférée (4-6 segments)
strategic_range = [4, 5, 6]
print(f"\n🎯 Plage stratégique préférée : {strategic_range} segments")

# Analyse des scores dans la plage stratégique
strategic_scores = {}
for k in strategic_range:
    if k in k_range:
        idx = list(k_range).index(k)
        strategic_scores[k] = {
            'silhouette': silhouette_scores[idx],
            'calinski': calinski_scores[idx],
            'target_met': silhouette_scores[idx] > 0.4
        }
        status = "🎯" if silhouette_scores[idx] > 0.4 else "📊"
        print(f"   K = {k}: Silhouette = {silhouette_scores[idx]:.3f} {status}")

# Choix final basé sur la stratégie "First Purchase"
# Priorité 1: K dans la plage stratégique avec silhouette > 0.4
strategic_valid = [k for k in strategic_range if k in strategic_scores and strategic_scores[k]['target_met']]

if strategic_valid:
    # Prendre le K avec le meilleur score silhouette dans la plage stratégique
    optimal_k = max(strategic_valid, key=lambda k: strategic_scores[k]['silhouette'])
    justification = f"K dans la plage stratégique (4-6) avec silhouette > 0.4 (score: {strategic_scores[optimal_k]['silhouette']:.3f})"
elif strategic_range[0] in strategic_scores:
    # Priorité 2: Meilleur K dans la plage stratégique même si < 0.4
    strategic_k_scores = [(k, strategic_scores[k]['silhouette']) for k in strategic_range if k in strategic_scores]
    optimal_k = max(strategic_k_scores, key=lambda x: x[1])[0]
    justification = f"Meilleur K dans la plage stratégique (4-6), score: {strategic_scores[optimal_k]['silhouette']:.3f}"
else:
    # Priorité 3: Fallback sur les métriques classiques
    optimal_k = optimal_k_silhouette
    justification = f"Meilleur score silhouette global (hors plage stratégique): {max(silhouette_scores):.3f}"

print(f"\n🎯 CHOIX FINAL ADAPTÉ À LA STRATÉGIE : K = {optimal_k}")
print(f"📝 Justification : {justification}")

# Validation du choix
if optimal_k in strategic_range:
    print(f"✅ Choix conforme à la stratégie (4-6 segments)")
else:
    print(f"⚠️ Choix hors plage stratégique - Ajustement recommandé")

if optimal_k in k_range:
    final_silhouette = silhouette_scores[list(k_range).index(optimal_k)]
    if final_silhouette > 0.4:
        print(f"✅ Objectif qualité atteint (silhouette: {final_silhouette:.3f} > 0.4)")
    else:
        print(f"⚠️ Objectif qualité non atteint (silhouette: {final_silhouette:.3f} < 0.4)")

print(f"\n✅ Nombre de clusters sélectionné pour la suite : {optimal_k}")

# Application du clustering final avec k optimal
print(f"🔄 Application du clustering K-Means avec K = {optimal_k}...")

# Clustering final
final_kmeans = KMeans(
    n_clusters=optimal_k,
    random_state=SEED,
    n_init=20,  # Plus d'initialisations pour la stabilité
    max_iter=500,
    algorithm='lloyd'
)

# Prédiction des clusters
cluster_labels = final_kmeans.fit_predict(X_clustering)

# Ajout des labels aux datasets
X_with_clusters = X_clustering.copy()
X_with_clusters['cluster'] = cluster_labels

# Ajout aux datasets complets
df_complete_with_clusters = df_complete.copy()
df_complete_with_clusters['cluster'] = cluster_labels

# Calcul des métriques finales
final_silhouette = silhouette_score(X_clustering, cluster_labels)
final_calinski = calinski_harabasz_score(X_clustering, cluster_labels)
final_inertia = final_kmeans.inertia_

print(f"\n📊 Métriques du clustering final :")
print(f"   Score de silhouette : {final_silhouette:.3f}")
print(f"   Score Calinski-Harabasz : {final_calinski:.0f}")
print(f"   Inertie : {final_inertia:.2f}")

# Validation des objectifs
if final_silhouette > 0.4:
    print(f"   ✅ Objectif qualité atteint (silhouette > 0.4)")
else:
    print(f"   ⚠️ Objectif qualité non atteint (silhouette < 0.4)")

# Distribution des clusters
cluster_counts = pd.Series(cluster_labels).value_counts().sort_index()
print(f"\n📊 Distribution des clusters :")
for cluster_id, count in cluster_counts.items():
    percentage = (count / len(cluster_labels)) * 100
    print(f"   Cluster {cluster_id}: {count:,} clients ({percentage:.1f}%)")

# Vérification de l'équilibre des clusters
min_size = cluster_counts.min()
max_size = cluster_counts.max()
balance_ratio = min_size / max_size

print(f"\n🔍 Équilibre des clusters :")
print(f"   Ratio min/max : {balance_ratio:.2f}")
if balance_ratio > 0.3:  # Seuil d'équilibre raisonnable
    print(f"   ✅ Clusters relativement équilibrés")
else:
    print(f"   ⚠️ Clusters déséquilibrés - Certains segments très petits")

print(f"\n✅ Clustering terminé avec {optimal_k} segments")

# Analyse des profils "First Purchase" par segment
print("📊 Analyse des profils 'First Purchase' par segment...")

# Calcul des moyennes par cluster sur les variables originales
cluster_profiles = df_complete_with_clusters.groupby('cluster').agg({
    col: ['mean', 'median', 'std'] for col in clustering_features
}).round(3)

# Affichage des profils
print(f"\n📋 Profils des segments (moyennes) :")
for cluster_id in sorted(df_complete_with_clusters['cluster'].unique()):
    cluster_data = df_complete_with_clusters[df_complete_with_clusters['cluster'] == cluster_id]
    cluster_size = len(cluster_data)

    print(f"\n🎯 SEGMENT {cluster_id} ({cluster_size:,} clients - {cluster_size/len(df_complete_with_clusters)*100:.1f}%) :")

    # Analyse par variable "First Purchase"
    for feature in clustering_features:
        if feature in cluster_data.columns:
            mean_val = cluster_data[feature].mean()
            median_val = cluster_data[feature].median()

            # Comparaison avec la moyenne globale
            global_mean = df_complete_with_clusters[feature].mean()
            ratio = mean_val / global_mean if global_mean != 0 else 0

            if ratio > 1.2:
                trend = "📈 Élevé"
            elif ratio < 0.8:
                trend = "📉 Faible"
            else:
                trend = "📊 Moyen"

            print(f"   {feature}: {mean_val:.2f} (médiane: {median_val:.2f}) {trend}")

# Identification automatique des profils selon la stratégie
print(f"\n🎯 IDENTIFICATION DES PROFILS SELON LA STRATÉGIE :")

segment_names = {}
for cluster_id in sorted(df_complete_with_clusters['cluster'].unique()):
    cluster_data = df_complete_with_clusters[df_complete_with_clusters['cluster'] == cluster_id]

    # Calcul des caractéristiques moyennes
    features_means = {}
    for feature in clustering_features:
        if feature in cluster_data.columns:
            global_mean = df_complete_with_clusters[feature].mean()
            cluster_mean = cluster_data[feature].mean()
            features_means[feature] = cluster_mean / global_mean if global_mean != 0 else 1

    # Logique d'identification des profils
    profile_name = "Segment Générique"
    profile_description = "Profil non identifié"

    # Premium Newcomers : récence faible + montant élevé + satisfaction haute
    if (features_means.get('recency_days', 1) < 0.8 and
        features_means.get('order_value', 1) > 1.3 and
        features_means.get('review_score_filled', 1) > 1.1):
        profile_name = "Premium Newcomers"
        profile_description = "Clients récents, gros montants, très satisfaits"

    # Value Seekers : montant faible
    elif features_means.get('order_value', 1) < 0.7:
        profile_name = "Value Seekers"
        profile_description = "Clients sensibles au prix, petits montants"

    # Express Shoppers : délai de livraison faible
    elif features_means.get('delivery_days', 1) < 0.8:
        profile_name = "Express Shoppers"
        profile_description = "Clients privilégiant la livraison rapide"

    # Regional Shoppers : concentration géographique
    elif features_means.get('state_encoded', 1) > 1.2 or features_means.get('state_encoded', 1) < 0.8:
        profile_name = "Regional Shoppers"
        profile_description = "Clients avec pattern géographique spécifique"

    # Seasonal Buyers : pattern temporel marqué
    elif features_means.get('purchase_month', 1) > 1.2 or features_means.get('purchase_month', 1) < 0.8:
        profile_name = "Seasonal Buyers"
        profile_description = "Clients avec comportement saisonnier"

    segment_names[cluster_id] = {
        'name': profile_name,
        'description': profile_description,
        'size': len(cluster_data)
    }

    print(f"\n🏷️ Segment {cluster_id}: {profile_name}")
    print(f"   📝 {profile_description}")
    print(f"   👥 {len(cluster_data):,} clients ({len(cluster_data)/len(df_complete_with_clusters)*100:.1f}%)")

print(f"\n✅ Analyse des profils terminée")

# Export des résultats de clustering "First Purchase"
print("💾 Export des résultats de clustering 'First Purchase'...")

# Création des dossiers nécessaires
os.makedirs('data/processed', exist_ok=True)
os.makedirs('reports/analysis', exist_ok=True)
os.makedirs('models', exist_ok=True)

# 1. Sauvegarde du modèle K-Means
model_path = 'models/3_01_kmeans_first_purchase.pkl'
joblib.dump(final_kmeans, model_path)
print(f"✅ Modèle K-Means sauvegardé : {model_path}")

# 2. Dataset complet avec clusters et profils
df_complete_with_clusters['segment_name'] = df_complete_with_clusters['cluster'].map(
    lambda x: segment_names[x]['name']
)
df_complete_with_clusters['segment_description'] = df_complete_with_clusters['cluster'].map(
    lambda x: segment_names[x]['description']
)

clustered_data_path = 'data/processed/3_01_customers_clustered_first_purchase.csv'
df_complete_with_clusters.to_csv(clustered_data_path, index=False)
print(f"✅ Dataset avec clusters sauvegardé : {clustered_data_path}")

# 3. Résumé des segments
segments_summary = []
for cluster_id, info in segment_names.items():
    cluster_data = df_complete_with_clusters[df_complete_with_clusters['cluster'] == cluster_id]

    summary = {
        'cluster_id': cluster_id,
        'segment_name': info['name'],
        'description': info['description'],
        'size': len(cluster_data),
        'percentage': len(cluster_data) / len(df_complete_with_clusters) * 100
    }

    # Ajout des moyennes des variables clés
    for feature in clustering_features:
        if feature in cluster_data.columns:
            summary[f'{feature}_mean'] = cluster_data[feature].mean()

    segments_summary.append(summary)

segments_df = pd.DataFrame(segments_summary)
segments_summary_path = 'data/processed/3_02_segments_summary_first_purchase.csv'
segments_df.to_csv(segments_summary_path, index=False)
print(f"✅ Résumé des segments sauvegardé : {segments_summary_path}")

# 4. Métadonnées complètes du clustering
clustering_metadata = {
    'clustering_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
    'approach': 'First Purchase Segmentation',
    'algorithm': 'KMeans',
    'n_clusters': optimal_k,
    'features_used': clustering_features,
    'metrics': {
        'silhouette_score': final_silhouette,
        'calinski_harabasz_score': final_calinski,
        'inertia': final_inertia,
        'balance_ratio': balance_ratio
    },
    'segments': segment_names,
    'strategy_compliance': {
        'target_segments': '4-6',
        'actual_segments': optimal_k,
        'target_silhouette': 0.4,
        'actual_silhouette': final_silhouette,
        'strategy_met': optimal_k in [4, 5, 6] and final_silhouette > 0.4
    }
}

metadata_path = 'reports/analysis/3_03_clustering_metadata_first_purchase.json'
with open(metadata_path, 'w') as f:
    json.dump(clustering_metadata, f, indent=2, default=str)
print(f"✅ Métadonnées sauvegardées : {metadata_path}")

print(f"\n📊 Résumé final :")
print(f"   🎯 Segments créés : {optimal_k}")
print(f"   📈 Score silhouette : {final_silhouette:.3f}")
print(f"   👥 Clients segmentés : {len(df_complete_with_clusters):,}")
print(f"   📋 Variables utilisées : {len(clustering_features)}")

# Validation finale de la stratégie
strategy_success = optimal_k in [4, 5, 6] and final_silhouette > 0.4
if strategy_success:
    print(f"\n🎉 STRATÉGIE 'FIRST PURCHASE' RÉUSSIE !")
    print(f"   ✅ Nombre de segments conforme (4-6)")
    print(f"   ✅ Qualité atteinte (silhouette > 0.4)")
else:
    print(f"\n⚠️ Stratégie partiellement atteinte")
    if optimal_k not in [4, 5, 6]:
        print(f"   ❌ Nombre de segments hors cible : {optimal_k} (attendu: 4-6)")
    if final_silhouette <= 0.4:
        print(f"   ❌ Qualité insuffisante : {final_silhouette:.3f} (attendu: > 0.4)")

print(f"\n✅ Export terminé - Prêt pour le Notebook 4 (Analyse Marketing)")