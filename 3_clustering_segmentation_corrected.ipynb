{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Notebook 3 : Clustering & Segmentation \"First Purchase\"\n", "\n", "## Objectif\n", "Appliquer des algorithmes de clustering adaptés au contexte \"First Purchase\" sur les variables optimisées. Créer 4-6 segments équilibrés avec un score de silhouette > 0.4, et identifier les profils clients pour l'acquisition et la réactivation.\n", "\n", "### Segments attendus selon la stratégie :\n", "- **Premium Newcomers** : R<PERSON><PERSON>s + montant élevé + satisfaction haute\n", "- **Regional Shoppers** : Concentration géographique spécifique\n", "- **Seasonal Buyers** : <PERSON><PERSON><PERSON> temporels marqués\n", "- **Value Seekers** : Sensibles au prix, montant faible\n", "- **Express Shoppers** : Priorité à la livraison rapide\n", "\n", "---\n", "\n", "**Auteur :** <PERSON><PERSON>  \n", "**Date :** 3 juin 2025  \n", "**Projet :** Segmentation client Olist  "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Chargement des données préparées\n", "\n", "### 1.1 Import des librairies de clustering"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Imports spécifiques pour ce notebook\n", "import os\n", "import json\n", "from datetime import datetime\n", "import joblib\n", "\n", "# Clustering et métriques\n", "from sklearn.cluster import KMeans, DBSCAN, AgglomerativeClustering\n", "from sklearn.decomposition import PCA\n", "from sklearn.metrics import silhouette_score, calinski_harabasz_score\n", "from sklearn.preprocessing import StandardScaler\n", "from itertools import combinations\n", "from scipy.cluster.hierarchy import dendrogram, linkage\n", "from scipy import stats\n", "\n", "# Imports locaux - modules utils optimisés\n", "from utils.core import (\n", "    init_notebook, SEED, PROJECT_ROOT, REPORTS_DIR,\n", "    # Les imports de base sont déjà dans core\n", "    pd, np, plt, sns\n", ")\n", "from utils.clustering import (\n", "    find_optimal_k,\n", "    perform_kmeans,\n", "    perform_dbscan,\n", "    calculate_clustering_metrics,\n", "    analyze_clusters,\n", "    compare_clustering_algorithms\n", ")\n", "from utils.clustering_visualization import (\n", "    plot_elbow_curve,\n", "    plot_clusters_2d,\n", "    plot_cluster_profiles,\n", "    plot_cluster_feature_distributions,\n", "    plot_cluster_sizes,\n", "    export_figure\n", ")\n", "from utils.data_tools import load_data, export_artifact\n", "from utils.save_load import save_results, load_results\n", "\n", "# Configuration du notebook avec le module core\n", "init_notebook(\n", "    notebook_file_path=\"3_clustering_segmentation.ipynb\",\n", "    style=\"whitegrid\",\n", "    figsize=(12, 8),\n", "    random_seed=SEED,\n", "    setup=True,\n", "    check_deps=True\n", ")\n", "\n", "print(f\"\\n📁 Répertoire de travail : {PROJECT_ROOT}\")\n", "print(f\"📊 Répertoire des rapports : {REPORTS_DIR}\")\n", "print(f\"🎲 Graine aléatoire : {SEED}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.2 Import du jeu de données normalisé"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Chargement des données préparées du Notebook 2\n", "print(\"🔄 Chargement des données préparées pour le clustering...\")\n", "\n", "# Chemins des fichiers générés par le notebook 2\n", "data_path_scaled = 'data/processed/2_01_features_scaled_clustering.csv'\n", "data_path_with_ids = 'data/processed/2_02_features_scaled_with_ids.csv'\n", "data_path_complete = 'data/processed/2_03_rfm_enriched_complete.csv'\n", "\n", "try:\n", "    # Chargement des datasets\n", "    X_scaled = pd.read_csv(data_path_scaled)\n", "    X_with_ids = pd.read_csv(data_path_with_ids)\n", "    df_complete = pd.read_csv(data_path_complete)\n", "\n", "    print(f\"✅ Dataset pour clustering : {X_scaled.shape}\")\n", "    print(f\"   Variables : {list(X_scaled.columns)}\")\n", "    print(f\"\\n✅ Dataset avec IDs : {X_with_ids.shape}\")\n", "    print(f\"✅ Dataset complet : {df_complete.shape}\")\n", "\n", "    # Vérification de la cohérence\n", "    assert len(X_scaled) == len(X_with_ids) == len(df_complete), \"Tailles incohérentes entre les datasets\"\n", "    print(f\"\\n✅ Cohérence vérifiée : {len(X_scaled)} clients dans tous les datasets\")\n", "\n", "except FileNotFoundError as e:\n", "    print(f\"❌ Erreur : Fichier non trouvé - {e}\")\n", "    print(\"💡 Assurez-vous d'avoir exécuté le Notebook 2 (Feature Engineering) avant ce notebook.\")\n", "    raise\n", "except Exception as e:\n", "    print(f\"❌ Erreur lors du chargement : {e}\")\n", "    raise"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.3 Vérification des variables \"First Purchase\" pour la segmentation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Vérification des variables \"First Purchase\" pour la segmentation\n", "print(\"📊 Variables 'First Purchase' utilisées pour la segmentation :\")\n", "\n", "# Variables attendues selon la stratégie\n", "expected_features = ['recency_days', 'order_value', 'state_encoded', \n", "                    'purchase_month', 'delivery_days', 'review_score_filled']\n", "\n", "print(f\"\\n🎯 Variables attendues selon la stratégie ({len(expected_features)}) :\")\n", "for i, feature in enumerate(expected_features, 1):\n", "    status = \"✅\" if feature in X_scaled.columns else \"❌\"\n", "    print(f\"{i:2d}. {feature} {status}\")\n", "\n", "print(f\"\\n📋 Variables réellement disponibles ({len(X_scaled.columns)}) :\")\n", "for i, col in enumerate(X_scaled.columns, 1):\n", "    expected = \"(attendue)\" if col in expected_features else \"(non attendue)\"\n", "    print(f\"{i:2d}. {col} {expected}\")\n", "\n", "# Vérification de la conformité à la stratégie\n", "available_expected = [f for f in expected_features if f in X_scaled.columns]\n", "missing_expected = [f for f in expected_features if f not in X_scaled.columns]\n", "unexpected_features = [f for f in X_scaled.columns if f not in expected_features]\n", "\n", "print(f\"\\n🔍 Analyse de conformité à la stratégie :\")\n", "print(f\"   ✅ Variables attendues présentes : {len(available_expected)}/{len(expected_features)}\")\n", "if missing_expected:\n", "    print(f\"   ❌ Variables attendues manquantes : {missing_expected}\")\n", "if unexpected_features:\n", "    print(f\"   ⚠️ Variables non attendues : {unexpected_features}\")\n", "\n", "# Validation du nombre de variables (6 maximum selon stratégie)\n", "if len(X_scaled.columns) <= 6:\n", "    print(f\"   ✅ Nombre de variables respecté : {len(X_scaled.columns)}/6 max\")\n", "else:\n", "    print(f\"   ⚠️ Trop de variables : {len(X_scaled.columns)}/6 max (stratégie non respectée)\")\n", "\n", "# Variables finales pour clustering (exclure les constantes)\n", "variables_to_check = X_scaled.columns[X_scaled.std() > 0].tolist()\n", "constant_variables = X_scaled.columns[X_scaled.std() == 0].tolist()\n", "\n", "if constant_variables:\n", "    print(f\"\\n⚠️ Variables constantes détectées (exclues du clustering) : {constant_variables}\")\n", "\n", "clustering_features = variables_to_check\n", "print(f\"\\n🎯 Variables finales pour clustering ({len(clustering_features)}) : {clustering_features}\")\n", "\n", "# Vérification de la normalisation\n", "if len(clustering_features) > 0:\n", "    mean_max = X_scaled[clustering_features].mean().abs().max()\n", "    std_mean = X_scaled[clustering_features].std().mean()\n", "    print(f\"\\n🔍 Vérification normalisation : moyennes={mean_max:.6f}, écarts-types={std_mean:.6f}\")\n", "    \n", "    if mean_max < 1e-10 and 0.9 <= std_mean <= 1.1:\n", "        print(\"   ✅ Normalisation correcte\")\n", "    else:\n", "        print(\"   ⚠️ Normalisation potentiellement incorrecte\")\n", "else:\n", "    print(\"   ❌ Aucune variable valide pour clustering\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Recherche du nombre optimal de clusters\n", "\n", "### 2.1 Méthode du coude adaptée au contexte \"First Purchase\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Méthode du coude adaptée au contexte \"First Purchase\"\n", "print(\"🔍 Recherche du nombre optimal de clusters pour segmentation 'First Purchase'...\")\n", "print(f\"🎯 Objectif : 4-6 segments équilibrés avec score silhouette > 0.4\")\n", "\n", "# Vérification des données pour clustering\n", "if 'clustering_features' not in locals() or len(clustering_features) == 0:\n", "    print(\"❌ Aucune variable valide pour clustering détectée\")\n", "    print(\"💡 Utilisation de toutes les variables disponibles\")\n", "    clustering_features = X_scaled.columns.tolist()\n", "\n", "# Dataset final pour clustering\n", "X_clustering = X_scaled[clustering_features].copy()\n", "print(f\"\\n📊 Dataset pour clustering : {X_clustering.shape}\")\n", "print(f\"📋 Variables utilisées : {clustering_features}\")\n", "\n", "# Plage de k adaptée à la stratégie (4-6 segments attendus)\n", "k_range = range(3, 8)  # 3 à 7 pour tester autour de 4-6\n", "inertias = []\n", "silhouette_scores = []\n", "\n", "# Optimisation : utiliser un sous-ensemble pour le score de silhouette\n", "sample_size = min(10000, len(X_clustering))  # Limiter à 10000 points max\n", "if sample_size < len(X_clustering):\n", "    print(f\"📊 Utilisation d'un échantillon de {sample_size} points pour le score de silhouette\")\n", "    X_sample = X_clustering.sample(n=sample_size, random_state=SEED)\n", "else:\n", "    X_sample = X_clustering\n", "\n", "print(\"\\n🔄 Calcul en cours...\")\n", "for k in k_range:\n", "    print(f\"  K = {k}\", end=\" \")\n", "\n", "    # K-Means avec paramètres optimisés\n", "    kmeans = KMeans(\n", "        n_clusters=k,\n", "        random_state=SEED,\n", "        n_init=10,  # Augmenté pour plus de stabilité\n", "        max_iter=300,\n", "        algorithm='lloyd'  # Plus stable pour les petits datasets\n", "    )\n", "    cluster_labels = kmeans.fit_predict(X_clustering)\n", "\n", "    # Calcul du score de silhouette sur l'échantillon\n", "    sample_labels = kmeans.predict(X_sample)\n", "    silhouette_avg = silhouette_score(X_sample, sample_labels)\n", "\n", "    inertias.append(kmeans.inertia_)\n", "    silhouette_scores.append(silhouette_avg)\n", "\n", "    # Indication si le score atteint l'objectif\n", "    target_met = \"🎯\" if silhouette_avg > 0.4 else \"📊\"\n", "    print(f\"(Silhouette: {silhouette_avg:.3f} {target_met})\")\n", "\n", "print(\"\\n✅ Calculs terminés\")\n", "\n", "# Identification des k qui atteignent l'objectif\n", "target_k_values = [k for k, score in zip(k_range, silhouette_scores) if score > 0.4]\n", "if target_k_values:\n", "    print(f\"\\n🎯 Valeurs de K atteignant l'objectif (silhouette > 0.4) : {target_k_values}\")\n", "else:\n", "    print(f\"\\n⚠️ Aucune valeur de K n'atteint l'objectif (silhouette > 0.4)\")\n", "    print(f\"   Meilleur score obtenu : {max(silhouette_scores):.3f}\")\n", "\n", "# Utilisation du module de visualisation optimisé\n", "fig = plot_elbow_curve(k_range, inertias, silhouette_scores)\n", "export_figure(fig, notebook_name=\"3\", export_number=1, base_name=\"elbow_curve_first_purchase\")\n", "\n", "# Affichage des résultats\n", "print(f\"\\n📊 Inerties par k : {dict(zip(k_range, [round(i, 2) for i in inertias]))}\")\n", "print(f\"📊 Scores silhouette : {dict(zip(k_range, [round(s, 3) for s in silhouette_scores]))}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.2 Choix du k optimal selon la stratégie \"First Purchase\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calcul des métriques complémentaires\n", "print(\"\\n🔍 Calcul des métriques complémentaires...\")\n", "\n", "calinski_scores = []\n", "for k in k_range:\n", "    # Recalcul pour obtenir les labels\n", "    kmeans = KMeans(n_clusters=k, random_state=SEED, n_init=10, max_iter=300)\n", "    cluster_labels = kmeans.fit_predict(X_clustering)\n", "    \n", "    # Score de Calinski-<PERSON><PERSON><PERSON>\n", "    calinski_score = calinski_harabasz_score(X_clustering, cluster_labels)\n", "    calinski_scores.append(calinski_score)\n", "\n", "# Identification des k optimaux par métrique\n", "optimal_k_silhouette = k_range[np.argmax(silhouette_scores)]\n", "optimal_k_calinski = k_range[np.argmax(calinski_scores)]\n", "\n", "# Plage stratégique préférée (4-6 segments)\n", "strategic_range = [4, 5, 6]\n", "print(f\"\\n🎯 Plage stratégique préférée : {strategic_range} segments\")\n", "\n", "# Analyse des scores dans la plage stratégique\n", "strategic_scores = {}\n", "for k in strategic_range:\n", "    if k in k_range:\n", "        idx = list(k_range).index(k)\n", "        strategic_scores[k] = {\n", "            'silhouette': silhouette_scores[idx],\n", "            'calinski': calinski_scores[idx],\n", "            'target_met': silhouette_scores[idx] > 0.4\n", "        }\n", "        status = \"🎯\" if silhouette_scores[idx] > 0.4 else \"📊\"\n", "        print(f\"   K = {k}: Silhouette = {silhouette_scores[idx]:.3f} {status}\")\n", "\n", "# Choix final basé sur la stratégie \"First Purchase\"\n", "# Priorité 1: K dans la plage stratégique avec silhouette > 0.4\n", "strategic_valid = [k for k in strategic_range if k in strategic_scores and strategic_scores[k]['target_met']]\n", "\n", "if strategic_valid:\n", "    # <PERSON><PERSON><PERSON> le K avec le meilleur score silhouette dans la plage stratégique\n", "    optimal_k = max(strategic_valid, key=lambda k: strategic_scores[k]['silhouette'])\n", "    justification = f\"K dans la plage stratégique (4-6) avec silhouette > 0.4 (score: {strategic_scores[optimal_k]['silhouette']:.3f})\"\n", "elif strategic_range[0] in strategic_scores:\n", "    # Priorité 2: <PERSON><PERSON><PERSON> K dans la plage stratégique même si < 0.4\n", "    strategic_k_scores = [(k, strategic_scores[k]['silhouette']) for k in strategic_range if k in strategic_scores]\n", "    optimal_k = max(strategic_k_scores, key=lambda x: x[1])[0]\n", "    justification = f\"Meilleur K dans la plage stratégique (4-6), score: {strategic_scores[optimal_k]['silhouette']:.3f}\"\n", "else:\n", "    # Priorité 3: Fallback sur les métriques classiques\n", "    optimal_k = optimal_k_silhouette\n", "    justification = f\"Meilleur score silhouette global (hors plage stratégique): {max(silhouette_scores):.3f}\"\n", "\n", "print(f\"\\n🎯 CHOIX FINAL ADAPTÉ À LA STRATÉGIE : K = {optimal_k}\")\n", "print(f\"📝 Justification : {justification}\")\n", "\n", "# Validation du choix\n", "if optimal_k in strategic_range:\n", "    print(f\"✅ Choix conforme à la stratégie (4-6 segments)\")\n", "else:\n", "    print(f\"⚠️ Choix hors plage stratégique - Ajustement recommandé\")\n", "\n", "if optimal_k in k_range:\n", "    final_silhouette = silhouette_scores[list(k_range).index(optimal_k)]\n", "    if final_silhouette > 0.4:\n", "        print(f\"✅ Objectif qualité atteint (silhouette: {final_silhouette:.3f} > 0.4)\")\n", "    else:\n", "        print(f\"⚠️ Objectif qualité non atteint (silhouette: {final_silhouette:.3f} < 0.4)\")\n", "\n", "print(f\"\\n✅ Nombre de clusters sélectionné pour la suite : {optimal_k}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Clustering final et analyse des segments\n", "\n", "### 3.1 Application du clustering K-Means avec k optimal"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Application du clustering final avec k optimal\n", "print(f\"🔄 Application du clustering K-Means avec K = {optimal_k}...\")\n", "\n", "# Clustering final\n", "final_kmeans = KMeans(\n", "    n_clusters=optimal_k,\n", "    random_state=SEED,\n", "    n_init=20,  # Plus d'initialisations pour la stabilité\n", "    max_iter=500,\n", "    algorithm='lloyd'\n", ")\n", "\n", "# Prédiction des clusters\n", "cluster_labels = final_kmeans.fit_predict(X_clustering)\n", "\n", "# Ajout des labels aux datasets\n", "X_with_clusters = X_clustering.copy()\n", "X_with_clusters['cluster'] = cluster_labels\n", "\n", "# Ajout aux datasets complets\n", "df_complete_with_clusters = df_complete.copy()\n", "df_complete_with_clusters['cluster'] = cluster_labels\n", "\n", "# Calcul des métriques finales\n", "final_silhouette = silhouette_score(X_clustering, cluster_labels)\n", "final_calinski = calinski_harabasz_score(X_clustering, cluster_labels)\n", "final_inertia = final_kmeans.inertia_\n", "\n", "print(f\"\\n📊 Métriques du clustering final :\")\n", "print(f\"   Score de silhouette : {final_silhouette:.3f}\")\n", "print(f\"   Score Calinski-Harabasz : {final_calinski:.0f}\")\n", "print(f\"   Inertie : {final_inertia:.2f}\")\n", "\n", "# Validation des objectifs\n", "if final_silhouette > 0.4:\n", "    print(f\"   ✅ Objectif qualité atteint (silhouette > 0.4)\")\n", "else:\n", "    print(f\"   ⚠️ Objectif qualité non atteint (silhouette < 0.4)\")\n", "\n", "# Distribution des clusters\n", "cluster_counts = pd.Series(cluster_labels).value_counts().sort_index()\n", "print(f\"\\n📊 Distribution des clusters :\")\n", "for cluster_id, count in cluster_counts.items():\n", "    percentage = (count / len(cluster_labels)) * 100\n", "    print(f\"   Cluster {cluster_id}: {count:,} clients ({percentage:.1f}%)\")\n", "\n", "# Vérification de l'équilibre des clusters\n", "min_size = cluster_counts.min()\n", "max_size = cluster_counts.max()\n", "balance_ratio = min_size / max_size\n", "\n", "print(f\"\\n🔍 Équilibre des clusters :\")\n", "print(f\"   Ratio min/max : {balance_ratio:.2f}\")\n", "if balance_ratio > 0.3:  # Seuil d'équilibre raisonnable\n", "    print(f\"   ✅ Clusters relativement équilibrés\")\n", "else:\n", "    print(f\"   ⚠️ Clusters déséquilibrés - Certains segments très petits\")\n", "\n", "print(f\"\\n✅ Clustering terminé avec {optimal_k} segments\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.2 Ana<PERSON><PERSON> des profils \"First Purchase\" par segment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyse des profils \"First Purchase\" par segment\n", "print(\"📊 Analyse des profils 'First Purchase' par segment...\")\n", "\n", "# Calcul des moyennes par cluster sur les variables originales\n", "cluster_profiles = df_complete_with_clusters.groupby('cluster').agg({\n", "    col: ['mean', 'median', 'std'] for col in clustering_features\n", "}).round(3)\n", "\n", "# Affichage des profils\n", "print(f\"\\n📋 Profils des segments (moyennes) :\")\n", "for cluster_id in sorted(df_complete_with_clusters['cluster'].unique()):\n", "    cluster_data = df_complete_with_clusters[df_complete_with_clusters['cluster'] == cluster_id]\n", "    cluster_size = len(cluster_data)\n", "    \n", "    print(f\"\\n🎯 SEGMENT {cluster_id} ({cluster_size:,} clients - {cluster_size/len(df_complete_with_clusters)*100:.1f}%) :\")\n", "    \n", "    # Analyse par variable \"First Purchase\"\n", "    for feature in clustering_features:\n", "        if feature in cluster_data.columns:\n", "            mean_val = cluster_data[feature].mean()\n", "            median_val = cluster_data[feature].median()\n", "            \n", "            # Comparaison avec la moyenne globale\n", "            global_mean = df_complete_with_clusters[feature].mean()\n", "            ratio = mean_val / global_mean if global_mean != 0 else 0\n", "            \n", "            if ratio > 1.2:\n", "                trend = \"📈 Élevé\"\n", "            elif ratio < 0.8:\n", "                trend = \"📉 Faible\"\n", "            else:\n", "                trend = \"📊 Moyen\"\n", "            \n", "            print(f\"   {feature}: {mean_val:.2f} (médiane: {median_val:.2f}) {trend}\")\n", "\n", "# Identification automatique des profils selon la stratégie\n", "print(f\"\\n🎯 IDENTIFICATION DES PROFILS SELON LA STRATÉGIE :\")\n", "\n", "segment_names = {}\n", "for cluster_id in sorted(df_complete_with_clusters['cluster'].unique()):\n", "    cluster_data = df_complete_with_clusters[df_complete_with_clusters['cluster'] == cluster_id]\n", "    \n", "    # Calcul des caractéristiques moyennes\n", "    features_means = {}\n", "    for feature in clustering_features:\n", "        if feature in cluster_data.columns:\n", "            global_mean = df_complete_with_clusters[feature].mean()\n", "            cluster_mean = cluster_data[feature].mean()\n", "            features_means[feature] = cluster_mean / global_mean if global_mean != 0 else 1\n", "    \n", "    # Logique d'identification des profils\n", "    profile_name = \"Segment Générique\"\n", "    profile_description = \"Profil non identifié\"\n", "    \n", "    # Premium Newcomers : ré<PERSON> faible + montant élevé + satisfaction haute\n", "    if (features_means.get('recency_days', 1) < 0.8 and \n", "        features_means.get('order_value', 1) > 1.3 and \n", "        features_means.get('review_score_filled', 1) > 1.1):\n", "        profile_name = \"Premium Newcomers\"\n", "        profile_description = \"C<PERSON>s récents, gros montants, très satisfaits\"\n", "    \n", "    # Value Seekers : montant faible\n", "    elif features_means.get('order_value', 1) < 0.7:\n", "        profile_name = \"Value Seekers\"\n", "        profile_description = \"Clients sensibles au prix, petits montants\"\n", "    \n", "    # Express Shoppers : <PERSON><PERSON><PERSON> de l<PERSON> faible\n", "    elif features_means.get('delivery_days', 1) < 0.8:\n", "        profile_name = \"Express Shoppers\"\n", "        profile_description = \"Clients privilégiant la livraison rapide\"\n", "    \n", "    # Regional Shoppers : concentration géographique\n", "    elif features_means.get('state_encoded', 1) > 1.2 or features_means.get('state_encoded', 1) < 0.8:\n", "        profile_name = \"Regional Shoppers\"\n", "        profile_description = \"Clients avec pattern géographique spécifique\"\n", "    \n", "    # Seasonal Buyers : pattern temporel marqué\n", "    elif features_means.get('purchase_month', 1) > 1.2 or features_means.get('purchase_month', 1) < 0.8:\n", "        profile_name = \"Seasonal Buyers\"\n", "        profile_description = \"Clients avec comportement saisonnier\"\n", "    \n", "    segment_names[cluster_id] = {\n", "        'name': profile_name,\n", "        'description': profile_description,\n", "        'size': len(cluster_data)\n", "    }\n", "    \n", "    print(f\"\\n🏷️ Segment {cluster_id}: {profile_name}\")\n", "    print(f\"   📝 {profile_description}\")\n", "    print(f\"   👥 {len(cluster_data):,} clients ({len(cluster_data)/len(df_complete_with_clusters)*100:.1f}%)\")\n", "\n", "print(f\"\\n✅ Analyse des profils terminée\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Export et sauvegarde\n", "\n", "### 4.1 Export des résultats de clustering \"First Purchase\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Export des résultats de clustering \"First Purchase\"\n", "print(\"💾 Export des résultats de clustering 'First Purchase'...\")\n", "\n", "# Création des dossiers nécessaires\n", "os.makedirs('data/processed', exist_ok=True)\n", "os.makedirs('reports/analysis', exist_ok=True)\n", "os.makedirs('models', exist_ok=True)\n", "\n", "# 1. <PERSON><PERSON><PERSON><PERSON> du modèle K-Means\n", "model_path = 'models/3_01_kmeans_first_purchase.pkl'\n", "joblib.dump(final_kmeans, model_path)\n", "print(f\"✅ Modèle K-Means sauvegardé : {model_path}\")\n", "\n", "# 2. Dataset complet avec clusters et profils\n", "df_complete_with_clusters['segment_name'] = df_complete_with_clusters['cluster'].map(\n", "    lambda x: segment_names[x]['name']\n", ")\n", "df_complete_with_clusters['segment_description'] = df_complete_with_clusters['cluster'].map(\n", "    lambda x: segment_names[x]['description']\n", ")\n", "\n", "clustered_data_path = 'data/processed/3_01_customers_clustered_first_purchase.csv'\n", "df_complete_with_clusters.to_csv(clustered_data_path, index=False)\n", "print(f\"✅ Dataset avec clusters sauvegardé : {clustered_data_path}\")\n", "\n", "# 3. <PERSON><PERSON><PERSON><PERSON> segments\n", "segments_summary = []\n", "for cluster_id, info in segment_names.items():\n", "    cluster_data = df_complete_with_clusters[df_complete_with_clusters['cluster'] == cluster_id]\n", "    \n", "    summary = {\n", "        'cluster_id': cluster_id,\n", "        'segment_name': info['name'],\n", "        'description': info['description'],\n", "        'size': len(cluster_data),\n", "        'percentage': len(cluster_data) / len(df_complete_with_clusters) * 100\n", "    }\n", "    \n", "    # Ajout des moyennes des variables clés\n", "    for feature in clustering_features:\n", "        if feature in cluster_data.columns:\n", "            summary[f'{feature}_mean'] = cluster_data[feature].mean()\n", "    \n", "    segments_summary.append(summary)\n", "\n", "segments_df = pd.DataFrame(segments_summary)\n", "segments_summary_path = 'data/processed/3_02_segments_summary_first_purchase.csv'\n", "segments_df.to_csv(segments_summary_path, index=False)\n", "print(f\"✅ Résumé des segments sauvegardé : {segments_summary_path}\")\n", "\n", "# 4. Métadonnées complètes du clustering\n", "clustering_metadata = {\n", "    'clustering_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),\n", "    'approach': 'First Purchase Segmentation',\n", "    'algorithm': 'K<PERSON><PERSON><PERSON>',\n", "    'n_clusters': optimal_k,\n", "    'features_used': clustering_features,\n", "    'metrics': {\n", "        'silhouette_score': final_silhouette,\n", "        'calinski_harabasz_score': final_calinski,\n", "        'inertia': final_inertia,\n", "        'balance_ratio': balance_ratio\n", "    },\n", "    'segments': segment_names,\n", "    'strategy_compliance': {\n", "        'target_segments': '4-6',\n", "        'actual_segments': optimal_k,\n", "        'target_silhouette': 0.4,\n", "        'actual_silhouette': final_silhouette,\n", "        'strategy_met': optimal_k in [4, 5, 6] and final_silhouette > 0.4\n", "    }\n", "}\n", "\n", "metadata_path = 'reports/analysis/3_03_clustering_metadata_first_purchase.json'\n", "with open(metadata_path, 'w') as f:\n", "    json.dump(clustering_metadata, f, indent=2, default=str)\n", "print(f\"✅ Métadonnées sauvegardées : {metadata_path}\")\n", "\n", "print(f\"\\n📊 Résumé final :\")\n", "print(f\"   🎯 Segments créés : {optimal_k}\")\n", "print(f\"   📈 Score silhouette : {final_silhouette:.3f}\")\n", "print(f\"   👥 Clients segmentés : {len(df_complete_with_clusters):,}\")\n", "print(f\"   📋 Variables utilisées : {len(clustering_features)}\")\n", "\n", "# Validation finale de la stratégie\n", "strategy_success = optimal_k in [4, 5, 6] and final_silhouette > 0.4\n", "if strategy_success:\n", "    print(f\"\\n🎉 STRATÉGIE 'FIRST PURCHASE' RÉUSSIE !\")\n", "    print(f\"   ✅ Nombre de segments conforme (4-6)\")\n", "    print(f\"   ✅ Qualité atteinte (silhouette > 0.4)\")\n", "else:\n", "    print(f\"\\n⚠️ Stratégie partiellement atteinte\")\n", "    if optimal_k not in [4, 5, 6]:\n", "        print(f\"   ❌ Nombre de segments hors cible : {optimal_k} (attendu: 4-6)\")\n", "    if final_silhouette <= 0.4:\n", "        print(f\"   ❌ Qualité insuffisante : {final_silhouette:.3f} (attendu: > 0.4)\")\n", "\n", "print(f\"\\n✅ Export terminé - Prêt pour le Notebook 4 (Analyse Marketing)\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}