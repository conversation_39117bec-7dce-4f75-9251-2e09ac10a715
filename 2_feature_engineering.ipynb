# Imports spécifiques pour ce notebook
import os
import sqlite3
from datetime import datetime, timedelta

# Preprocessing spécifique
from sklearn.preprocessing import StandardScaler, LabelEncoder

# Imports locaux - modules utils optimisés
from utils.core import (
    init_notebook, SEED, PROJECT_ROOT, REPORTS_DIR,
    # Les imports de base sont déjà dans core
    pd, np, plt, sns
)
from utils.feature_engineering import (
    create_first_purchase_features,
    clean_and_impute_features,
    analyze_feature_correlation
)
from utils.data_tools import load_data, export_artifact
from utils.save_load import save_results
from utils.clustering_visualization import export_figure

# Configuration du notebook avec le module core
init_notebook(
    notebook_file_path="2_feature_engineering.ipynb",
    style="whitegrid",
    figsize=(12, 8),
    random_seed=SEED,
    setup=True,
    check_deps=True
)

print(f"\n📁 Répertoire de travail : {PROJECT_ROOT}")
print(f"📊 Répertoire des rapports : {REPORTS_DIR}")
print(f"🎲 Graine aléatoire : {SEED}")

# Chargement des données nettoyées du Notebook 1
print("🔄 Chargement des données nettoyées du Notebook 1...")

# Chemin du fichier généré par le Notebook 1
cleaned_data_path = 'data/processed/1_01_cleaned_dataset.csv'

# Vérification de l'existence du fichier
if not os.path.exists(cleaned_data_path):
    print(f"❌ Fichier non trouvé : {cleaned_data_path}")
    print("⚠️ Veuillez d'abord exécuter le Notebook 1 pour générer les données nettoyées.")
    print("\n🔄 Chargement alternatif depuis la base SQLite...")

    # Fallback : chargement depuis SQLite si le fichier n'existe pas
    db_path = 'data/raw/olist.db'
    if not os.path.exists(db_path):
        raise FileNotFoundError(f"❌ Base de données non trouvée : {db_path}")

    conn = sqlite3.connect(db_path)
    query = """
    SELECT
        o.customer_id,
        o.order_id,
        o.order_purchase_timestamp,
        o.order_delivered_customer_date,
        o.order_status,
        oi.price,
        c.customer_state,
        r.review_score
    FROM orders o
    LEFT JOIN order_items oi ON o.order_id = oi.order_id
    LEFT JOIN customers c ON o.customer_id = c.customer_id
    LEFT JOIN order_reviews r ON o.order_id = r.order_id
    WHERE o.order_status = 'delivered'
      AND o.order_delivered_customer_date IS NOT NULL
    """

    df_raw = pd.read_sql_query(query, conn)
    conn.close()
    print(f"✅ Données chargées depuis SQLite (fallback)")
else:
    # Chargement normal depuis le fichier nettoyé
    df_raw = pd.read_csv(cleaned_data_path)
    print(f"✅ Données chargées depuis le Notebook 1 : {cleaned_data_path}")

print(f"📊 Shape : {df_raw.shape}")
print(f"📋 Colonnes : {list(df_raw.columns)}")

# Vérification de l'intégrité
print(f"\n🔍 Vérifications :")
print(f"- Valeurs manquantes : {df_raw.isnull().sum().sum()}")
print(f"- Doublons : {df_raw.duplicated().sum()}")

# Conversion des dates si nécessaire
date_cols = ['order_purchase_timestamp', 'order_delivered_customer_date']
for col in date_cols:
    if col in df_raw.columns and not pd.api.types.is_datetime64_any_dtype(df_raw[col]):
        df_raw[col] = pd.to_datetime(df_raw[col])

# Vérification de la période si les colonnes de dates existent
if 'order_purchase_timestamp' in df_raw.columns:
    print(f"- Période des données : du {df_raw['order_purchase_timestamp'].min()} au {df_raw['order_purchase_timestamp'].max()}")

# Vérification du pattern "1 client = 1 commande"
if 'customer_id' in df_raw.columns and 'order_id' in df_raw.columns:
    orders_per_customer = df_raw.groupby('customer_id')['order_id'].nunique()
    multi_order_customers = (orders_per_customer > 1).sum()
    print(f"\n🎯 Pattern découvert :")
    print(f"- Clients avec >1 commande : {multi_order_customers}")
    print(f"- Confirmation : {'✅ Chaque client = 1 commande' if multi_order_customers == 0 else '⚠️ Certains clients ont plusieurs commandes'}")

# Préparation des données pour l'approche "First Purchase"
if 'df_raw' in locals():
    print("🔄 Préparation des données pour feature engineering...")

    # Vérification des colonnes nécessaires
    required_cols = ['customer_id', 'order_purchase_timestamp', 'price', 'customer_state']
    missing_cols = [col for col in required_cols if col not in df_raw.columns]

    if missing_cols:
        print(f"⚠️ Colonnes manquantes : {missing_cols}")
        print(f"Colonnes disponibles : {list(df_raw.columns)}")
    else:
        print("✅ Toutes les colonnes nécessaires sont présentes")

    # Agrégation par client (somme des prix par commande)
    # Gestion flexible des colonnes selon ce qui est disponible
    group_cols = ['customer_id']
    if 'order_id' in df_raw.columns:
        group_cols.append('order_id')
    if 'order_purchase_timestamp' in df_raw.columns:
        group_cols.append('order_purchase_timestamp')
    if 'order_delivered_customer_date' in df_raw.columns:
        group_cols.append('order_delivered_customer_date')
    if 'customer_state' in df_raw.columns:
        group_cols.append('customer_state')

    agg_dict = {}
    if 'price' in df_raw.columns:
        agg_dict['price'] = 'sum'
    if 'review_score' in df_raw.columns:
        agg_dict['review_score'] = 'mean'

    if agg_dict:
        df_aggregated = df_raw.groupby(group_cols).agg(agg_dict).reset_index()
        print(f"📊 Données agrégées : {df_aggregated.shape}")

        # Aperçu des données
        print(f"📋 Aperçu des données :")
        display(df_aggregated.head())

        # Statistiques descriptives
        numeric_cols = df_aggregated.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) > 0:
            print(f"\n📊 Statistiques descriptives :")
            display(df_aggregated[numeric_cols].describe())

        # Vérification des valeurs manquantes
        print(f"\n🔍 Valeurs manquantes par colonne :")
        missing_values = df_aggregated.isnull().sum()
        for col, count in missing_values.items():
            if count > 0:
                print(f"- {col}: {count} ({count/len(df_aggregated)*100:.1f}%)")

        if missing_values.sum() == 0:
            print("✅ Aucune valeur manquante détectée")
    else:
        print("⚠️ Aucune colonne numérique trouvée pour l'agrégation")
        df_aggregated = df_raw.copy()

# Création des features "First Purchase" avec le module utils
if 'df_aggregated' in locals():
    print("🔄 Création des features 'First Purchase' optimisées...")

    # Vérification de l'existence de la fonction dans le module utils
    try:
        # Test d'import de la fonction
        from utils.feature_engineering import create_first_purchase_features
        print("✅ Fonction create_first_purchase_features trouvée dans utils")

        # Utilisation de la fonction du module utils
        first_purchase_features = create_first_purchase_features(
            df=df_aggregated,
            customer_id_col='customer_id',
            date_col='order_purchase_timestamp',
            amount_col='price',
            state_col='customer_state',
            delivery_date_col='order_delivered_customer_date' if 'order_delivered_customer_date' in df_aggregated.columns else None,
            review_col='review_score' if 'review_score' in df_aggregated.columns else None
        )

        print(f"✅ Features 'First Purchase' créées : {first_purchase_features.shape}")
        print(f"📋 Variables créées : {list(first_purchase_features.columns)}")

    except ImportError as e:
        print(f"⚠️ Fonction create_first_purchase_features non trouvée : {e}")
        print("🔄 Création manuelle des features...")

        # Création manuelle des features si la fonction n'existe pas
        first_purchase_features = df_aggregated.copy()

        # 1. Récence (jours depuis l'achat)
        if 'order_purchase_timestamp' in first_purchase_features.columns:
            reference_date = first_purchase_features['order_purchase_timestamp'].max()
            first_purchase_features['recency_days'] = (
                reference_date - first_purchase_features['order_purchase_timestamp']
            ).dt.days

        # 2. Montant de la commande
        if 'price' in first_purchase_features.columns:
            first_purchase_features['order_value'] = first_purchase_features['price']

        # 3. État encodé
        if 'customer_state' in first_purchase_features.columns:
            le = LabelEncoder()
            first_purchase_features['state_encoded'] = le.fit_transform(
                first_purchase_features['customer_state'].fillna('Unknown')
            )

        # 4. Mois d'achat
        if 'order_purchase_timestamp' in first_purchase_features.columns:
            first_purchase_features['purchase_month'] = first_purchase_features['order_purchase_timestamp'].dt.month

        # 5. Délai de livraison
        if 'order_delivered_customer_date' in first_purchase_features.columns and 'order_purchase_timestamp' in first_purchase_features.columns:
            first_purchase_features['delivery_days'] = (
                first_purchase_features['order_delivered_customer_date'] -
                first_purchase_features['order_purchase_timestamp']
            ).dt.days

        # 6. Score de review rempli
        if 'review_score' in first_purchase_features.columns:
            median_score = first_purchase_features['review_score'].median()
            first_purchase_features['review_score_filled'] = first_purchase_features['review_score'].fillna(median_score)

        print(f"✅ Features créées manuellement : {first_purchase_features.shape}")
        print(f"📋 Variables disponibles : {list(first_purchase_features.columns)}")

    # Aperçu des features créées
    print(f"\n📊 Aperçu des features :")
    display(first_purchase_features.head())

    # Statistiques descriptives
    numeric_cols = first_purchase_features.select_dtypes(include=[np.number]).columns
    if len(numeric_cols) > 0:
        print(f"\n📈 Statistiques descriptives :")
        display(first_purchase_features[numeric_cols].describe())

# Sélection et nettoyage des features pour clustering
if 'first_purchase_features' in locals():
    print("🔄 Sélection et nettoyage des features pour clustering...")

    # Variables attendues selon la stratégie (6 maximum)
    target_features = ['customer_id', 'recency_days', 'order_value', 'state_encoded',
                      'purchase_month', 'delivery_days', 'review_score_filled']

    # Sélection des features disponibles
    available_features = [col for col in target_features if col in first_purchase_features.columns]
    missing_features = [col for col in target_features if col not in first_purchase_features.columns]

    print(f"📊 Features disponibles ({len(available_features)}) : {available_features}")
    if missing_features:
        print(f"⚠️ Features manquantes ({len(missing_features)}) : {missing_features}")

    # Création du dataset final avec les features disponibles
    features_clean = first_purchase_features[available_features].copy()

    # Nettoyage des valeurs manquantes
    print(f"\n🔍 Nettoyage des valeurs manquantes...")
    missing_before = features_clean.isnull().sum().sum()
    print(f"- Valeurs manquantes avant nettoyage : {missing_before}")

    # Gestion des valeurs manquantes par type de variable
    for col in features_clean.columns:
        if col != 'customer_id' and features_clean[col].isnull().sum() > 0:
            if features_clean[col].dtype in ['int64', 'float64']:
                # Variables numériques : médiane
                median_val = features_clean[col].median()
                features_clean[col] = features_clean[col].fillna(median_val)
                print(f"  - {col}: {features_clean[col].isnull().sum()} NaN remplis avec médiane ({median_val:.2f})")
            else:
                # Variables catégorielles : mode
                mode_val = features_clean[col].mode().iloc[0] if len(features_clean[col].mode()) > 0 else 'Unknown'
                features_clean[col] = features_clean[col].fillna(mode_val)
                print(f"  - {col}: {features_clean[col].isnull().sum()} NaN remplis avec mode ({mode_val})")

    missing_after = features_clean.isnull().sum().sum()
    print(f"- Valeurs manquantes après nettoyage : {missing_after}")

    # Identification des variables numériques pour clustering
    numeric_cols = [col for col in features_clean.columns
                   if col != 'customer_id' and features_clean[col].dtype in ['int64', 'float64']]

    # Vérification des variables à variance nulle
    zero_variance_vars = []
    for col in numeric_cols:
        if features_clean[col].std() == 0:
            zero_variance_vars.append(col)

    if zero_variance_vars:
        print(f"\n⚠️ Variables à variance nulle détectées : {zero_variance_vars}")
        print("Ces variables seront exclues du clustering.")
        numeric_cols = [col for col in numeric_cols if col not in zero_variance_vars]
    else:
        print(f"\n✅ Aucune variable à variance nulle détectée")

    clustering_vars = numeric_cols
    print(f"\n📋 Variables finales pour clustering ({len(clustering_vars)}) : {clustering_vars}")

    # Vérification des corrélations élevées
    if len(clustering_vars) > 1:
        corr_matrix = features_clean[clustering_vars].corr()
        high_corr_pairs = []
        for i in range(len(clustering_vars)):
            for j in range(i+1, len(clustering_vars)):
                corr_val = abs(corr_matrix.iloc[i, j])
                if corr_val > 0.7:
                    high_corr_pairs.append((clustering_vars[i], clustering_vars[j], corr_val))

        if high_corr_pairs:
            print(f"\n⚠️ Corrélations élevées détectées (>0.7) :")
            for var1, var2, corr in high_corr_pairs:
                print(f"  - {var1} ↔ {var2}: {corr:.3f}")
        else:
            print(f"\n✅ Aucune corrélation élevée détectée (seuil: 0.7)")

# Standardisation des features pour clustering
if 'features_clean' in locals() and 'clustering_vars' in locals():
    print("🔄 Standardisation des variables pour clustering...")

    if len(clustering_vars) == 0:
        print("❌ Aucune variable numérique disponible pour le clustering")
    else:
        # Préparation des données pour clustering (exclure customer_id)
        X_clustering = features_clean[clustering_vars].copy()

        print(f"📊 Variables à standardiser : {len(clustering_vars)}")
        print(f"📋 Variables : {clustering_vars}")

        # Standardisation avec StandardScaler
        scaler = StandardScaler()
        X_scaled = pd.DataFrame(
            scaler.fit_transform(X_clustering),
            columns=clustering_vars,
            index=X_clustering.index
        )

        print(f"\n✅ Standardisation terminée : {X_scaled.shape}")

        # Vérification de la standardisation
        print(f"\n📈 Vérification de la standardisation :")
        print(f"- Moyennes (doivent être ~0) : {X_scaled.mean().mean():.6f}")
        print(f"- Écarts-types (doivent être ~1) : {X_scaled.std().mean():.6f}")

        # Ajout de customer_id pour traçabilité
        X_scaled_with_id = X_scaled.copy()
        if 'customer_id' in features_clean.columns:
            X_scaled_with_id['customer_id'] = features_clean['customer_id'].values

        print(f"\n📊 Dataset final pour clustering : {X_scaled.shape}")
        print(f"📊 Dataset avec IDs : {X_scaled_with_id.shape}")

        # Aperçu des données standardisées
        print(f"\n📋 Aperçu des données standardisées :")
        display(X_scaled.head())

        # Statistiques descriptives
        print(f"\n📈 Statistiques descriptives :")
        display(X_scaled.describe())

# Export des datasets pour le clustering - NOMS CORRIGÉS pour Notebook 3
if 'X_scaled' in locals() and 'X_scaled_with_id' in locals():
    print("💾 Export des datasets pour le clustering...")

    # Création du dossier de sortie
    os.makedirs('data/processed', exist_ok=True)

    # 1. Dataset normalisé pour clustering (sans customer_id)
    clustering_data_path = 'data/processed/2_01_features_scaled_clustering.csv'
    X_scaled.to_csv(clustering_data_path, index=False)
    print(f"✅ Dataset clustering sauvegardé : {clustering_data_path}")

    # 2. Dataset avec customer_id pour traçabilité
    traceability_path = 'data/processed/2_02_features_scaled_with_ids.csv'
    X_scaled_with_id.to_csv(traceability_path, index=False)
    print(f"✅ Dataset avec IDs sauvegardé : {traceability_path}")

    # 3. Dataset complet enrichi (NOM CORRIGÉ pour Notebook 3)
    # Le Notebook 3 attend '2_03_rfm_enriched_complete.csv'
    complete_features_path = 'data/processed/2_03_rfm_enriched_complete.csv'
    features_clean.to_csv(complete_features_path, index=False)
    print(f"✅ Dataset complet sauvegardé : {complete_features_path}")

    # 4. Sauvegarde du scaler pour usage futur
    import joblib
    scaler_path = 'data/processed/2_04_scaler.pkl'
    joblib.dump(scaler, scaler_path)
    print(f"✅ Scaler sauvegardé : {scaler_path}")

    # Résumé final
    print(f"\n📊 Résumé final :")
    print(f"- Variables créées : {len(features_clean.columns)} (dont customer_id)")
    print(f"- Variables pour clustering : {len(clustering_vars)}")
    print(f"- Clients traités : {len(X_scaled):,}")
    print(f"- Variables finales : {clustering_vars}")

    print(f"\n✅ Feature engineering terminé avec succès !")
    print(f"📁 Fichiers prêts pour le Notebook 3 (Clustering)")
    print(f"\n📁 Fichiers exportés (compatibles Notebook 3) :")
    print(f"  - {clustering_data_path}")
    print(f"  - {traceability_path}")
    print(f"  - {complete_features_path}")
    print(f"  - {scaler_path}")