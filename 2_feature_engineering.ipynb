# Imports spécifiques pour ce notebook
import os
import sqlite3
from datetime import datetime, timedelta

# Preprocessing spécifique
from sklearn.preprocessing import StandardScaler, LabelEncoder

# Imports locaux - modules utils optimisés
from utils.core import (
    init_notebook, SEED, PROJECT_ROOT, REPORTS_DIR,
    # Les imports de base sont déjà dans core
    pd, np, plt, sns
)
from utils.feature_engineering import (
    create_first_purchase_features,
    clean_and_impute_features,
    analyze_feature_correlation
)
from utils.data_tools import load_data, export_artifact
from utils.save_load import save_results
from utils.clustering_visualization import export_figure

# Configuration du notebook avec le module core
init_notebook(
    notebook_file_path="2_feature_engineering.ipynb",
    style="whitegrid",
    figsize=(12, 8),
    random_seed=SEED,
    setup=True,
    check_deps=True
)

print(f"\n📁 Répertoire de travail : {PROJECT_ROOT}")
print(f"📊 Répertoire des rapports : {REPORTS_DIR}")
print(f"🎲 Graine aléatoire : {SEED}")

# Chargement des données depuis la base SQLite
print("🔄 Chargement des données depuis la base SQLite...")

# Connexion à la base de données
db_path = 'data/raw/olist.db'
conn = sqlite3.connect(db_path)

# Requête pour récupérer les données nécessaires
query = """
SELECT
    o.customer_id,
    o.order_id,
    o.order_purchase_timestamp,
    o.order_delivered_customer_date,
    o.order_status,
    oi.price,
    c.customer_state,
    r.review_score
FROM orders o
LEFT JOIN order_items oi ON o.order_id = oi.order_id
LEFT JOIN customers c ON o.customer_id = c.customer_id
LEFT JOIN order_reviews r ON o.order_id = r.order_id
WHERE o.order_status = 'delivered'
  AND o.order_delivered_customer_date IS NOT NULL
"""

# Chargement des données
df_raw = pd.read_sql_query(query, conn)
conn.close()

print(f"✅ Données chargées depuis la base SQLite")
print(f"📊 Shape : {df_raw.shape}")
print(f"📋 Colonnes : {list(df_raw.columns)}")

# Vérification de l'intégrité
print(f"\n🔍 Vérifications :")
print(f"- Valeurs manquantes : {df_raw.isnull().sum().sum()}")
print(f"- Doublons : {df_raw.duplicated().sum()}")

# Conversion des dates
df_raw['order_purchase_timestamp'] = pd.to_datetime(df_raw['order_purchase_timestamp'])
df_raw['order_delivered_customer_date'] = pd.to_datetime(df_raw['order_delivered_customer_date'])

print(f"- Période des données : du {df_raw['order_purchase_timestamp'].min()} au {df_raw['order_purchase_timestamp'].max()}")

# Vérification du pattern "1 client = 1 commande"
orders_per_customer = df_raw.groupby('customer_id')['order_id'].nunique()
multi_order_customers = (orders_per_customer > 1).sum()
print(f"\n🎯 Pattern découvert :")
print(f"- Clients avec >1 commande : {multi_order_customers}")
print(f"- Confirmation : {'✅ Chaque client = 1 commande' if multi_order_customers == 0 else '⚠️ Certains clients ont plusieurs commandes'}")

# Préparation des données pour l'approche "First Purchase"
if 'df_raw' in locals():
    print("🔄 Préparation des données pour feature engineering...")

    # Agrégation par client (somme des prix par commande)
    df_aggregated = df_raw.groupby(['customer_id', 'order_id', 'order_purchase_timestamp',
                                   'order_delivered_customer_date', 'customer_state']).agg({
        'price': 'sum',
        'review_score': 'mean'
    }).reset_index()

    print(f"📊 Données agrégées : {df_aggregated.shape}")
    print(f"📋 Aperçu des données :")
    display(df_aggregated.head())

    # Statistiques descriptives
    print(f"\n📊 Statistiques descriptives :")
    display(df_aggregated[['price', 'review_score']].describe())

    # Vérification des valeurs manquantes
    print(f"\n🔍 Valeurs manquantes par colonne :")
    missing_values = df_aggregated.isnull().sum()
    for col, count in missing_values.items():
        if count > 0:
            print(f"- {col}: {count} ({count/len(df_aggregated)*100:.1f}%)")

    if missing_values.sum() == 0:
        print("✅ Aucune valeur manquante détectée")

# Création des features "First Purchase" avec le module utils
if 'df_aggregated' in locals():
    print("🔄 Création des features 'First Purchase' optimisées...")

    # Utilisation de la fonction du module utils
    first_purchase_features = create_first_purchase_features(
        df=df_aggregated,
        customer_id_col='customer_id',
        date_col='order_purchase_timestamp',
        amount_col='price',
        state_col='customer_state',
        delivery_date_col='order_delivered_customer_date',
        review_col='review_score'
    )

    print(f"✅ Features 'First Purchase' créées : {first_purchase_features.shape}")
    print(f"📋 Variables créées : {list(first_purchase_features.columns)}")

    # Aperçu des features créées
    print(f"\n📊 Aperçu des features :")
    display(first_purchase_features.head())

    # Statistiques descriptives
    print(f"\n📈 Statistiques descriptives :")
    display(first_purchase_features.describe())

# Validation des features "First Purchase" créées
if 'first_purchase_features' in locals():
    print("🔍 Validation des features 'First Purchase'...")

    # Vérification des 6 variables attendues
    expected_features = ['customer_id', 'recency_days', 'order_value', 'state_encoded',
                        'purchase_month', 'delivery_days', 'review_score_filled']

    missing_features = [f for f in expected_features if f not in first_purchase_features.columns]
    if missing_features:
        print(f"⚠️ Features manquantes : {missing_features}")
    else:
        print("✅ Toutes les features attendues sont présentes")

    # Analyse de la qualité des features
    print(f"\n📊 Analyse de qualité :")
    for col in first_purchase_features.columns:
        if col != 'customer_id':
            null_count = first_purchase_features[col].isnull().sum()
            zero_var = first_purchase_features[col].std() == 0
            print(f"- {col}: {null_count} NaN, variance nulle: {zero_var}")

    # Vérification des corrélations
    numeric_cols = first_purchase_features.select_dtypes(include=[np.number]).columns.tolist()
    if 'customer_id' in numeric_cols:
        numeric_cols.remove('customer_id')

    if len(numeric_cols) > 1:
        high_corr_pairs = analyze_feature_correlation(first_purchase_features, numeric_cols, threshold=0.7)
        if high_corr_pairs:
            print(f"\n⚠️ Corrélations élevées détectées :")
            for feat1, feat2, corr in high_corr_pairs:
                print(f"- {feat1} ↔ {feat2}: {corr:.3f}")
        else:
            print(f"\n✅ Aucune corrélation élevée détectée (seuil: 0.7)")

# Visualisation des distributions des features "First Purchase"
if 'first_purchase_features' in locals():
    print("📊 Visualisation des distributions des features 'First Purchase'...")

    # Création de la figure avec 6 sous-graphiques (2x3)
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('Distribution des Variables "First Purchase"', fontsize=16, fontweight='bold')

    # Variables à visualiser (exclure customer_id)
    viz_cols = ['recency_days', 'order_value', 'state_encoded',
               'purchase_month', 'delivery_days', 'review_score_filled']

    colors = ['skyblue', 'lightgreen', 'salmon', 'gold', 'lightcoral', 'plum']

    for i, (col, color) in enumerate(zip(viz_cols, colors)):
        row, col_idx = i // 3, i % 3

        if col in first_purchase_features.columns:
            data = first_purchase_features[col].dropna()

            # Histogramme
            axes[row, col_idx].hist(data, bins=30, alpha=0.7, color=color, edgecolor='black')
            axes[row, col_idx].set_title(f'Distribution de {col}')
            axes[row, col_idx].set_xlabel(col)
            axes[row, col_idx].set_ylabel('Nombre de clients')
            axes[row, col_idx].grid(True, alpha=0.3)

            # Ajout de statistiques sur le graphique
            mean_val = data.mean()
            median_val = data.median()
            axes[row, col_idx].axvline(mean_val, color='red', linestyle='--', alpha=0.7, label=f'Moyenne: {mean_val:.1f}')
            axes[row, col_idx].axvline(median_val, color='orange', linestyle='--', alpha=0.7, label=f'Médiane: {median_val:.1f}')
            axes[row, col_idx].legend(fontsize=8)
        else:
            axes[row, col_idx].text(0.5, 0.5, f'Variable {col}\nnon disponible',
                                   ha='center', va='center', transform=axes[row, col_idx].transAxes)
            axes[row, col_idx].set_title(f'{col} (non disponible)')

    plt.tight_layout()

    # Export de la figure selon les règles du projet
    export_figure(fig, notebook_name="2", export_number=1, base_name="first_purchase_distributions")
    plt.show()

# Nettoyage et préparation des features pour clustering
if 'first_purchase_features' in locals():
    print("🔄 Nettoyage des features pour clustering...")

    # Utilisation du module utils pour nettoyer les features
    features_clean, numeric_cols, categorical_cols = clean_and_impute_features(
        first_purchase_features, id_col='customer_id'
    )

    print(f"✅ Features nettoyées : {features_clean.shape}")
    print(f"📊 Variables numériques : {len(numeric_cols)}")
    print(f"📊 Variables catégorielles : {len(categorical_cols)}")

    # Vérification des valeurs manquantes après nettoyage
    missing_after = features_clean.isnull().sum().sum()
    print(f"\n🔍 Valeurs manquantes après nettoyage : {missing_after}")

    # Vérification des variables à variance nulle (problématiques pour clustering)
    zero_variance_vars = []
    for col in numeric_cols:
        if features_clean[col].std() == 0:
            zero_variance_vars.append(col)

    if zero_variance_vars:
        print(f"\n⚠️ Variables à variance nulle détectées : {zero_variance_vars}")
        print("Ces variables seront exclues du clustering.")
    else:
        print(f"\n✅ Aucune variable à variance nulle détectée")

    # Variables finales pour clustering (exclure les variables problématiques)
    clustering_vars = [col for col in numeric_cols if col not in zero_variance_vars]
    print(f"\n📋 Variables finales pour clustering ({len(clustering_vars)}) : {clustering_vars}")

# Standardisation des features pour clustering
if 'features_clean' in locals() and 'clustering_vars' in locals():
    print("🔄 Standardisation des variables pour clustering...")

    # Préparation des données pour clustering (exclure customer_id)
    X_clustering = features_clean[clustering_vars].copy()

    print(f"📊 Variables à standardiser : {len(clustering_vars)}")
    print(f"📋 Variables : {clustering_vars}")

    # Standardisation avec StandardScaler
    scaler = StandardScaler()
    X_scaled = pd.DataFrame(
        scaler.fit_transform(X_clustering),
        columns=clustering_vars,
        index=X_clustering.index
    )

    print(f"\n✅ Standardisation terminée : {X_scaled.shape}")

    # Vérification de la standardisation
    print(f"\n📈 Vérification de la standardisation :")
    print(f"- Moyennes (doivent être ~0) : {X_scaled.mean().mean():.6f}")
    print(f"- Écarts-types (doivent être ~1) : {X_scaled.std().mean():.6f}")

    # Ajout de customer_id pour traçabilité
    X_scaled_with_id = X_scaled.copy()
    X_scaled_with_id['customer_id'] = features_clean['customer_id'].values

    print(f"\n📊 Dataset final pour clustering : {X_scaled.shape}")
    print(f"📊 Dataset avec IDs : {X_scaled_with_id.shape}")

    # Aperçu des données standardisées
    print(f"\n📋 Aperçu des données standardisées :")
    display(X_scaled.head())

    # Statistiques descriptives
    print(f"\n📈 Statistiques descriptives :")
    display(X_scaled.describe())

# Export des datasets pour le clustering
if 'X_scaled' in locals() and 'X_scaled_with_id' in locals():
    print("💾 Export des datasets pour le clustering...")

    # Création du dossier de sortie
    os.makedirs('data/processed', exist_ok=True)

    # Dataset normalisé pour clustering (sans customer_id)
    clustering_data_path = 'data/processed/2_01_features_scaled_clustering.csv'
    X_scaled.to_csv(clustering_data_path, index=False)
    print(f"✅ Dataset clustering sauvegardé : {clustering_data_path}")

    # Dataset avec customer_id pour traçabilité
    traceability_path = 'data/processed/2_02_features_scaled_with_ids.csv'
    X_scaled_with_id.to_csv(traceability_path, index=False)
    print(f"✅ Dataset avec IDs sauvegardé : {traceability_path}")

    # Dataset original des features (non normalisé)
    original_features_path = 'data/processed/2_03_first_purchase_features.csv'
    first_purchase_features.to_csv(original_features_path, index=False)
    print(f"✅ Features originales sauvegardées : {original_features_path}")

    # Sauvegarde du scaler pour usage futur
    import joblib
    scaler_path = 'data/processed/2_04_scaler.pkl'
    joblib.dump(scaler, scaler_path)
    print(f"✅ Scaler sauvegardé : {scaler_path}")

    # Résumé final
    print(f"\n📊 Résumé final :")
    print(f"- Variables créées : {len(first_purchase_features.columns)} (dont customer_id)")
    print(f"- Variables pour clustering : {len(clustering_vars)}")
    print(f"- Clients traités : {len(X_scaled):,}")
    print(f"- Variables finales : {list(clustering_vars)}")

    print(f"\n✅ Feature engineering terminé avec succès !")
    print(f"📁 Fichiers prêts pour le Notebook 3 (Clustering)")