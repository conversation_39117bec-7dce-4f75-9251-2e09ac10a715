{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Notebook 2 : Feature Engineering (RFM & comportements)\n", "\n", "## Objectif\n", "Construire des variables pertinentes pour la segmentation client. Utiliser le modèle RFM (Récence, Fréquence, Montant), compléter avec d'autres variables comportementales, et préparer les données pour le clustering.\n", "\n", "---\n", "\n", "**Auteur :** <PERSON><PERSON>  \n", "**Date :** 3 juin 2025  \n", "**Projet :** Segmentation client Olist  "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Chargement des données nettoyées\n", "\n", "### 1.1 Import des librairies nécessaires"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Imports spécifiques pour ce notebook\n", "import os\n", "import json\n", "from datetime import datetime, timedelta\n", "\n", "# Preprocessing spécifique\n", "from sklearn.preprocessing import StandardScaler, MinMaxScaler\n", "from sklearn.impute import SimpleImputer\n", "\n", "# Imports locaux - modules utils optimisés\n", "from utils.core import (\n", "    init_notebook, SEED, PROJECT_ROOT, REPORTS_DIR,\n", "    # Les imports de base sont déjà dans core\n", "    pd, np, plt, sns\n", ")\n", "from utils.feature_engineering import (\n", "    calculate_rfm,\n", "    create_temporal_features,\n", "    create_advanced_transactional_features,\n", "    consolidate_all_features,\n", "    prepare_clustering_features\n", ")\n", "from utils.data_tools import load_data, export_artifact\n", "from utils.save_load import save_results\n", "from utils.clustering_visualization import export_figure\n", "\n", "# Configuration du notebook avec le module core\n", "init_notebook(\n", "    notebook_file_path=\"2_feature_engineering.ipynb\",\n", "    style=\"whitegrid\",\n", "    figsize=(12, 8),\n", "    random_seed=SEED,\n", "    setup=True,\n", "    check_deps=True\n", ")\n", "\n", "print(f\"\\n📁 Répertoire de travail : {PROJECT_ROOT}\")\n", "print(f\"📊 Répertoire des rapports : {REPORTS_DIR}\")\n", "print(f\"🎲 Graine aléatoire : {SEED}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.2 Chargement du fichier nettoyé produit par le Notebook 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Chargement des données nettoyées du Notebook 1\n", "# Le fichier a été sauvegardé avec la convention de nommage du projet\n", "data_path = 'data/processed/1_01_cleaned_dataset.csv'\n", "\n", "# Vérification de l'existence du fichier\n", "if not os.path.exists(data_path):\n", "    print(f\"⚠️ Fichier non trouvé : {data_path}\")\n", "    print(\"Veuillez d'abord exécuter le Notebook 1 pour générer les données nettoyées.\")\n", "else:\n", "    # Chargement du dataset nettoyé\n", "    df_clean = pd.read_csv(data_path)\n", "    print(f\"✅ Dataset chargé depuis : {data_path}\")\n", "    print(f\"📊 Shape : {df_clean.shape}\")\n", "    print(f\"📋 Colonnes : {list(df_clean.columns)}\")\n", "\n", "    # Vérification de l'intégrité\n", "    print(f\"\\n🔍 Vérifications :\")\n", "    print(f\"- Valeurs manquantes : {df_clean.isnull().sum().sum()}\")\n", "    print(f\"- Doublons : {df_clean.duplicated().sum()}\")\n", "\n", "    # Vérification de la période des données avec le bon nom de colonne\n", "    # Utilisation de 'first_order_date' qui devrait être disponible d'après le code précédent\n", "    if 'first_order_date' in df_clean.columns:\n", "        print(f\"- Période des données : du {df_clean['first_order_date'].min()} au {df_clean['first_order_date'].max()}\")\n", "    else:\n", "        print(\"⚠️ Colonne de date non trouvée. Colonnes disponibles :\")\n", "        print(df_clean.columns.tolist())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.3 Vérifications d'usage"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Vérifications d'usage et préparation pour RFM\n", "if 'df_clean' in locals():\n", "    # Vérification des colonnes RFM déjà calculées\n", "    rfm_columns = ['frequency', 'recency', 'monetary']\n", "    missing_rfm = [col for col in rfm_columns if col not in df_clean.columns]\n", "\n", "    if missing_rfm:\n", "        print(f\"⚠️ Métriques RFM manquantes : {missing_rfm}\")\n", "        print(f\"Colonnes disponibles : {list(df_clean.columns)}\")\n", "    else:\n", "        print(\"✅ Toutes les métriques RFM sont présentes\")\n", "\n", "        # Aperçu des données\n", "        print(f\"\\n📋 Aperçu des données :\")\n", "        display(df_clean.head())\n", "\n", "        # Statistiques descriptives des métriques RFM\n", "        print(f\"\\n📊 Statistiques descriptives des métriques RFM :\")\n", "        display(df_clean[rfm_columns].describe())\n", "\n", "        # Vérification des segments\n", "        if 'value_segment' in df_clean.columns:\n", "            print(f\"\\n🎯 Distribution des segments :\")\n", "            segment_dist = df_clean['value_segment'].value_counts()\n", "            display(segment_dist)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Calcul des variables RFM\n", "\n", "### 2.1 Définition de la date de référence pour la récence"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Définition de la date de référence pour calculer la récence\n", "if 'df_clean' in locals():\n", "    # Conversion des colonnes de dates en datetime\n", "    df_clean['first_order_date'] = pd.to_datetime(df_clean['first_order_date'])\n", "    df_clean['last_order_date'] = pd.to_datetime(df_clean['last_order_date'])\n", "\n", "    # Date de référence = dernière date d'achat + 1 jour\n", "    reference_date = df_clean['last_order_date'].max() + <PERSON><PERSON><PERSON>(days=1)\n", "    print(f\"📅 Date de référence pour la récence : {reference_date}\")\n", "    print(f\"📊 Période d'analyse : du {df_clean['first_order_date'].min()} au {df_clean['last_order_date'].max()}\")\n", "\n", "    # Calcul de la durée totale de la période\n", "    period_duration = (df_clean['last_order_date'].max() - df_clean['first_order_date'].min()).days\n", "    print(f\"🕰️ Durée totale de la période : {period_duration} jours ({period_duration/365:.1f} années)\")\n", "\n", "    # Vérification de la cohérence avec la récence déjà calculée\n", "    if 'recency' in df_clean.columns:\n", "        print(f\"\\n🔍 Vérification de la récence :\")\n", "        print(f\"- <PERSON><PERSON><PERSON> moyenne : {df_clean['recency'].mean():.1f} jours\")\n", "        print(f\"- R<PERSON><PERSON> médiane : {df_clean['recency'].median():.1f} jours\")\n", "        print(f\"- Récence min : {df_clean['recency'].min():.1f} jours\")\n", "        print(f\"- Récence max : {df_clean['recency'].max():.1f} jours\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.2 Agrégation par client : calcul R, F, M"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calcul des métriques RFM par client\n", "if 'df_clean' in locals() and 'reference_date' in locals():\n", "    print(\"🔄 Vérification des métriques RFM existantes...\")\n", "\n", "    # Vérification des colonnes RFM existantes\n", "    rfm_columns = ['recency', 'frequency', 'monetary']\n", "    if all(col in df_clean.columns for col in rfm_columns):\n", "        print(\"✅ Les métriques RFM sont déjà calculées\")\n", "\n", "        # Création d'un DataFrame RFM à partir des métriques existantes\n", "        rfm_df = df_clean[['customer_id'] + rfm_columns].copy()\n", "\n", "        # Ajout des montants moyens si disponibles\n", "        if 'monetary' in df_clean.columns:\n", "            rfm_df['montant_moyen'] = df_clean['monetary'] / df_clean['frequency']\n", "\n", "        print(f\"📊 Données RFM disponibles pour {len(rfm_df):,} clients\")\n", "        print(f\"\\n📋 Aperçu des données RFM :\")\n", "        display(rfm_df.head())\n", "        print(f\"\\n📈 Statistiques descriptives RFM :\")\n", "        display(rfm_df.describe())\n", "\n", "        # Vérification de la cohérence des métriques\n", "        print(f\"\\n🔍 Vérification de la cohérence :\")\n", "        print(f\"- <PERSON><PERSON><PERSON> moyenne : {rfm_df['recency'].mean():.1f} jours\")\n", "        print(f\"- <PERSON><PERSON><PERSON> moyenne : {rfm_df['frequency'].mean():.1f} commandes\")\n", "        print(f\"- <PERSON><PERSON> moyen : {rfm_df['monetary'].mean():.2f} €\")\n", "    else:\n", "        print(\"⚠️ Métriques RFM incomplètes. Colonnes manquantes :\")\n", "        missing_cols = [col for col in rfm_columns if col not in df_clean.columns]\n", "        print(f\"- {missing_cols}\")\n", "        print(f\"Colonnes disponibles : {list(df_clean.columns)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.3 Construction et validation du DataFrame RFM final"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualisation des distributions RFM\n", "if 'rfm_df' in locals():\n", "    print(\"📊 Visualisation des distributions RFM...\")\n", "\n", "    # D<PERSON>terminer les noms de colonnes selon la source (utilitaire ou manuel)\n", "    if 'recence' in rfm_df.columns:\n", "        # Version manuelle\n", "        recency_col, frequency_col = 'recence', 'frequence'\n", "        monetary_total_col, monetary_avg_col = 'montant_total', 'montant_moyen'\n", "    else:\n", "        # Version utilitaire\n", "        recency_col, frequency_col = 'recency', 'frequency'\n", "        monetary_total_col, monetary_avg_col = 'monetary', 'monetary'\n", "\n", "    # C<PERSON>ation de la figure\n", "    fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "    fig.suptitle('Distribution des Variables RFM', fontsize=16, fontweight='bold')\n", "\n", "    # Récence\n", "    axes[0,0].hist(rfm_df[recency_col], bins=50, alpha=0.7, color='skyblue', edgecolor='black')\n", "    axes[0,0].set_title('Distribution de la Récence (jours)')\n", "    axes[0,0].set_xlabel('Jours depuis dernier achat')\n", "    axes[0,0].set_ylabel('Nombre de clients')\n", "    axes[0,0].grid(True, alpha=0.3)\n", "\n", "    # Fréquence\n", "    axes[0,1].hist(rfm_df[frequency_col], bins=30, alpha=0.7, color='lightgreen', edgecolor='black')\n", "    axes[0,1].set_title('Distribution de la Fréquence')\n", "    axes[0,1].set_xlabel('Nombre d\\'achats')\n", "    axes[0,1].set_ylabel('Nombre de clients')\n", "    axes[0,1].grid(True, alpha=0.3)\n", "\n", "    # Montant (utiliser la colonne appropriée)\n", "    if monetary_total_col in rfm_df.columns:\n", "        monetary_col = monetary_total_col\n", "        title_suffix = 'Total'\n", "    else:\n", "        monetary_col = monetary_avg_col\n", "        title_suffix = 'Moyen'\n", "\n", "    axes[1,0].hist(rfm_df[monetary_col], bins=50, alpha=0.7, color='salmon', edgecolor='black')\n", "    axes[1,0].set_title(f'Distribution du Montant {title_suffix}')\n", "    axes[1,0].set_xlabel('Montant (€)')\n", "    axes[1,0].set_ylabel('Nombre de clients')\n", "    axes[1,0].grid(True, alpha=0.3)\n", "\n", "    # Boxplot des variables RFM pour détecter les outliers\n", "    rfm_numeric = rfm_df.select_dtypes(include=[np.number])\n", "    axes[1,1].boxplot([rfm_numeric[col].dropna() for col in rfm_numeric.columns if col != 'customer_id'],\n", "                      labels=[col for col in rfm_numeric.columns if col != 'customer_id'])\n", "    axes[1,1].set_title('Boxplots des Variables RFM')\n", "    axes[1,1].set_ylabel('Valeurs (normalisées)')\n", "    axes[1,1].tick_params(axis='x', rotation=45)\n", "    axes[1,1].grid(True, alpha=0.3)\n", "\n", "    plt.tight_layout()\n", "\n", "    # Export de la figure selon les règles du projet\n", "    export_figure(fig, notebook_name=\"2\", export_number=1, base_name=\"rfm_distributions\")\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Enrichissement comportemental\n", "\n", "### 3.1 Calcul de l'ancienneté client"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calcul de l'ancienneté client et features temporelles\n", "if 'df_clean' in locals() and 'reference_date' in locals():\n", "    print(\"🔄 Calcul des features temporelles...\")\n", "\n", "    # Calcul manuel des features temporelles\n", "    print(\"🔄 Calcul des features temporelles...\")\n", "\n", "    # Calcul de l'ancienneté client\n", "    customer_lifetime = df_clean.groupby('customer_id').agg({\n", "        'first_order_date': 'min',\n", "        'last_order_date': 'max'\n", "    })\n", "\n", "    # Ancienneté = différence entre première et dernière commande\n", "    customer_lifetime['customer_lifespan_days'] = (\n", "        customer_lifetime['last_order_date'] - customer_lifetime['first_order_date']\n", "    ).dt.days\n", "\n", "    # Ancienneté depuis la première commande\n", "    customer_lifetime['days_since_first_order'] = (\n", "        reference_date - customer_lifetime['first_order_date']\n", "    ).dt.days\n", "\n", "    # Ajout de catégories d'ancienneté\n", "    customer_lifetime['customer_age_category'] = pd.cut(\n", "        customer_lifetime['days_since_first_order'],\n", "        bins=[0, 30, 90, 180, 365, float('inf')],\n", "        labels=['Nouveau (<30j)', '<PERSON><PERSON><PERSON> (30-90j)', '<PERSON><PERSON><PERSON><PERSON> (90-180j)',\n", "               'Ancien (180-365j)', 'Très ancien (>365j)']\n", "    )\n", "\n", "    temporal_features = customer_lifetime.reset_index()\n", "\n", "    print(f\"📊 Features temporelles calculées pour {len(temporal_features):,} clients\")\n", "    print(f\"\\n📈 Statistiques d'ancienneté :\")\n", "    print(f\"- Ancienneté moyenne : {temporal_features['customer_lifespan_days'].mean():.1f} jours\")\n", "    print(f\"- Ancienneté médiane : {temporal_features['customer_lifespan_days'].median():.1f} jours\")\n", "    print(f\"- Ancienneté max : {temporal_features['customer_lifespan_days'].max():.1f} jours\")\n", "\n", "    # Distribution des catégories d'ancienneté\n", "    print(f\"\\n📊 Distribution des catégories d'ancienneté :\")\n", "    age_distribution = temporal_features['customer_age_category'].value_counts()\n", "    for category, count in age_distribution.items():\n", "        pct = (count / len(temporal_features)) * 100\n", "        print(f\"- {category}: {count} clients ({pct:.1f}%)\")\n", "\n", "    display(temporal_features.head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.2 <PERSON><PERSON><PERSON> entre commandes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calcul des features transactionnelles avancées\n", "if 'df_clean' in locals():\n", "    print(\"🔄 Calcul des features transactionnelles avancées...\")\n", "\n", "    # Calcul des features à partir des métriques RFM existantes\n", "    transactional_features = df_clean.groupby('customer_id').agg({\n", "        'frequency': 'first',  # <PERSON><PERSON> de commandes\n", "        'monetary': ['first', 'std'],  # Montant total et écart-type\n", "        'recency': 'first'  # <PERSON><PERSON><PERSON> commande\n", "    }).reset_index()\n", "\n", "    # Renommage des colonnes\n", "    transactional_features.columns = ['customer_id', 'total_orders', 'total_amount', 'amount_std', 'recency']\n", "\n", "    # Calcul du montant moyen par commande\n", "    transactional_features['avg_order_value'] = (\n", "        transactional_features['total_amount'] / transactional_features['total_orders']\n", "    )\n", "\n", "    # Calcul du coefficient de variation des montants\n", "    transactional_features['amount_cv'] = (\n", "        transactional_features['amount_std'] / transactional_features['avg_order_value']\n", "    ).<PERSON>na(0)\n", "\n", "    # Cal<PERSON>l de la fréquence d'achat (commandes par jour)\n", "    transactional_features['purchase_frequency'] = (\n", "        transactional_features['total_orders'] / transactional_features['recency']\n", "    ).<PERSON>na(0)\n", "\n", "    print(f\"📊 Features transactionnelles calculées pour {len(transactional_features):,} clients\")\n", "\n", "    print(f\"\\n📈 Statistiques des montants :\")\n", "    print(f\"- <PERSON><PERSON> moyen par commande : {transactional_features['avg_order_value'].mean():.2f} €\")\n", "    print(f\"- Écart-type des montants : {transactional_features['amount_std'].mean():.2f} €\")\n", "    print(f\"- CV des montants : {transactional_features['amount_cv'].mean():.2f}\")\n", "\n", "    print(f\"\\n�� Statistiques de fréquence :\")\n", "    print(f\"- <PERSON><PERSON><PERSON> d'achat moyenne : {transactional_features['purchase_frequency'].mean():.3f} commandes/jour\")\n", "    print(f\"- Nombre moyen de commandes : {transactional_features['total_orders'].mean():.1f}\")\n", "\n", "    # Affichage des résultats\n", "    display(transactional_features.head())\n", "\n", "    # Vérification des valeurs aberrantes\n", "    print(f\"\\n🔍 Vérification des valeurs aberrantes :\")\n", "    for col in ['avg_order_value', 'amount_cv', 'purchase_frequency']:\n", "        q1 = transactional_features[col].quantile(0.25)\n", "        q3 = transactional_features[col].quantile(0.75)\n", "        iqr = q3 - q1\n", "        outliers = transactional_features[\n", "            (transactional_features[col] < q1 - 1.5 * iqr) |\n", "            (transactional_features[col] > q3 + 1.5 * iqr)\n", "        ]\n", "        print(f\"- {col}: {len(outliers)} valeurs aberrantes ({len(outliers)/len(transactional_features)*100:.1f}%)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.3 Nombre de catégories achetées et diversité"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calcul de la diversité des achats et variabilité\n", "if 'df_clean' in locals():\n", "    print(\"🔄 Calcul de la diversité des achats...\")\n", "\n", "    # Vérification des colonnes disponibles\n", "    print(\"📋 Colonnes disponibles :\")\n", "    print(df_clean.columns.tolist())\n", "\n", "    # Création du DataFrame de base avec customer_id\n", "    diversity_features = df_clean[['customer_id']].drop_duplicates().reset_index(drop=True)\n", "\n", "    # Calcul de la variabilité des montants d'achat\n", "    print(\"\\n📊 Calcul de la variabilité des montants...\")\n", "\n", "    # Vérification de la présence des colonnes nécessaires\n", "    if 'monetary' in df_clean.columns and 'frequency' in df_clean.columns:\n", "        # Calcul des statistiques de base\n", "        order_stats = df_clean.groupby('customer_id').agg({\n", "            'monetary': ['sum', 'mean', 'std', 'min', 'max'],\n", "            'frequency': 'first'\n", "        })\n", "\n", "        # Renommage des colonnes\n", "        order_stats.columns = ['total_amount', 'avg_amount', 'amount_std', 'min_amount', 'max_amount', 'total_orders']\n", "        order_stats = order_stats.reset_index()\n", "\n", "        # Calcul des métriques dérivées\n", "        order_stats['amount_cv'] = (order_stats['amount_std'] / order_stats['avg_amount']).fillna(0)\n", "        order_stats['amount_range'] = order_stats['max_amount'] - order_stats['min_amount']\n", "        order_stats['avg_order_value'] = order_stats['total_amount'] / order_stats['total_orders']\n", "\n", "        # Fusion avec le DataFrame de base\n", "        diversity_features = diversity_features.merge(order_stats, on='customer_id', how='left')\n", "\n", "        # Affichage des statistiques\n", "        print(f\"\\n📈 Statistiques des montants :\")\n", "        print(f\"- Montant total moyen : {order_stats['total_amount'].mean():.2f} €\")\n", "        print(f\"- <PERSON><PERSON> moyen par commande : {order_stats['avg_order_value'].mean():.2f} €\")\n", "        print(f\"- Écart-type moyen : {order_stats['amount_std'].mean():.2f} €\")\n", "        print(f\"- CV moyen : {order_stats['amount_cv'].mean():.3f}\")\n", "\n", "        # Vérification des valeurs aberrantes\n", "        print(f\"\\n🔍 Vérification des valeurs aberrantes :\")\n", "        for col in ['amount_std', 'amount_cv', 'amount_range']:\n", "            q1 = order_stats[col].quantile(0.25)\n", "            q3 = order_stats[col].quantile(0.75)\n", "            iqr = q3 - q1\n", "            outliers = order_stats[\n", "                (order_stats[col] < q1 - 1.5 * iqr) |\n", "                (order_stats[col] > q3 + 1.5 * iqr)\n", "            ]\n", "            print(f\"- {col}: {len(outliers)} valeurs aberrantes ({len(outliers)/len(order_stats)*100:.1f}%)\")\n", "\n", "        # Affichage des résultats\n", "        print(f\"\\n📊 Features calculées pour {len(diversity_features):,} clients\")\n", "        display(diversity_features.head())\n", "\n", "        # Statistiques descriptives\n", "        print(\"\\n📈 Statistiques descriptives :\")\n", "        display(diversity_features.describe())\n", "    else:\n", "        print(\"⚠️ Colonnes 'monetary' ou 'frequency' manquantes\")\n", "        print(\"Colonnes disponibles :\", df_clean.columns.tolist())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.4 <PERSON><PERSON> moyen, écart-type et ratios"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Consolidation de toutes les features\n", "if 'rfm_df' in locals():\n", "    print(\"🔄 Consolidation de toutes les features...\")\n", "\n", "    # Consolidation manuelle\n", "    rfm_enriched = rfm_df.copy()\n", "\n", "    # Fusion avec les features temporelles si disponibles\n", "    if 'temporal_features' in locals() and not temporal_features.empty:\n", "        rfm_enriched = rfm_enriched.merge(temporal_features, on='customer_id', how='left')\n", "        print(f\"+ {len(temporal_features.columns)-1} features temporelles a<PERSON>\")\n", "\n", "    # Fusion avec les features transactionnelles si disponibles\n", "    if 'transactional_features' in locals() and not transactional_features.empty:\n", "        rfm_enriched = rfm_enriched.merge(transactional_features, on='customer_id', how='left')\n", "        print(f\"+ {len(transactional_features.columns)-1} features transactionnelles ajoutées\")\n", "\n", "    # Fusion avec les features de diversité si disponibles\n", "    if 'diversity_features' in locals() and not diversity_features.empty:\n", "        rfm_enriched = rfm_enriched.merge(diversity_features, on='customer_id', how='left')\n", "        print(f\"+ {len(diversity_features.columns)-1} features de diversité ajoutées\")\n", "\n", "    # Nettoyage des colonnes en double\n", "    print(\"\\n🧹 Nettoyage des colonnes en double...\")\n", "\n", "    # Dictionnaire de mapping pour les colonnes en double\n", "    column_mapping = {\n", "        'recency_x': 'recency',\n", "        'recency_y': 'recency_days',\n", "        'total_orders_x': 'total_orders',\n", "        'total_orders_y': 'order_count',\n", "        'total_amount_x': 'total_amount',\n", "        'total_amount_y': 'amount_total',\n", "        'amount_std_x': 'amount_std',\n", "        'amount_std_y': 'amount_std_dev',\n", "        'amount_cv_x': 'amount_cv',\n", "        'amount_cv_y': 'amount_cv_coef',\n", "        'avg_order_value_x': 'avg_order_value',\n", "        'avg_order_value_y': 'order_value_mean'\n", "    }\n", "\n", "    # Renommage des colonnes\n", "    rfm_enriched = rfm_enriched.rename(columns=column_mapping)\n", "\n", "    # Suppression des colonnes en double\n", "    duplicate_cols = [col for col in rfm_enriched.columns if col.endswith(('_x', '_y'))]\n", "    rfm_enriched = rfm_enriched.drop(columns=duplicate_cols)\n", "\n", "    # Organisation des colonnes par catégorie\n", "    base_cols = ['customer_id']\n", "    rfm_cols = ['recency', 'frequency', 'monetary']\n", "    temporal_cols = [col for col in rfm_enriched.columns if any(x in col.lower() for x in ['date', 'days', 'age', 'lifespan'])]\n", "    monetary_cols = [col for col in rfm_enriched.columns if any(x in col.lower() for x in ['amount', 'monetary', 'value'])]\n", "    frequency_cols = [col for col in rfm_enriched.columns if any(x in col.lower() for x in ['frequency', 'orders', 'count'])]\n", "    other_cols = [col for col in rfm_enriched.columns if col not in base_cols + rfm_cols + temporal_cols + monetary_cols + frequency_cols]\n", "\n", "    # Réorganisation des colonnes\n", "    rfm_enriched = rfm_enriched[base_cols + rfm_cols + temporal_cols + monetary_cols + frequency_cols + other_cols]\n", "\n", "    print(f\"\\n📊 Dataset enrichi final : {rfm_enriched.shape}\")\n", "    print(\"\\n📋 Organisation des colonnes :\")\n", "    print(f\"- Colonnes de base : {base_cols}\")\n", "    print(f\"- Métriques RFM : {rfm_cols}\")\n", "    print(f\"- Features temporelles : {temporal_cols}\")\n", "    print(f\"- Features monétaires : {monetary_cols}\")\n", "    print(f\"- Features de fréquence : {frequency_cols}\")\n", "    print(f\"- Autres features : {other_cols}\")\n", "\n", "    # Affichage des statistiques descriptives\n", "    print(\"\\n📈 Statistiques descriptives :\")\n", "    display(rfm_enriched.describe())\n", "\n", "    # Vérification des valeurs manquantes\n", "    missing_values = rfm_enriched.isnull().sum()\n", "    if missing_values.any():\n", "        print(\"\\n⚠️ Valeurs manquantes par colonne :\")\n", "        print(missing_values[missing_values > 0])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.5 Taux de retour et indicateurs avancés (si applicable)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calcul d'indicateurs avancés : taux de retour et saisonnalité\n", "if 'df_clean' in locals():\n", "    print(\"🔄 Calcul des indicateurs avancés...\")\n", "\n", "    # Vérification des colonnes disponibles\n", "    print(\"📋 Colonnes disponibles :\")\n", "    print(df_clean.columns.tolist())\n", "\n", "    advanced_features = pd.DataFrame()\n", "\n", "    # Saisonnalité des achats\n", "    print(\"\\n📅 Calcul de la saisonnalité des achats...\")\n", "\n", "    # Utilisation des dates disponibles\n", "    if 'first_order_date' in df_clean.columns and 'recency' in df_clean.columns:\n", "        seasonal_patterns = df_clean.copy()\n", "\n", "        # Vérification des données\n", "        print(\"\\n🔍 Vérification des données :\")\n", "        print(f\"Récence moyenne : {seasonal_patterns['recency'].mean():.1f} jours\")\n", "        print(f\"Récence médiane : {seasonal_patterns['recency'].median():.1f} jours\")\n", "        print(f\"Récence max : {seasonal_patterns['recency'].max():.1f} jours\")\n", "\n", "        # Calcul des indicateurs temporels\n", "        temporal_features = pd.DataFrame()\n", "        temporal_features['customer_id'] = seasonal_patterns['customer_id']\n", "\n", "        # Période d'activité basée sur la récence\n", "        temporal_features['activity_period_days'] = seasonal_patterns['recency']\n", "        temporal_features['activity_period_months'] = temporal_features['activity_period_days'] / 30.44\n", "        temporal_features['activity_period_quarters'] = temporal_features['activity_period_days'] / 91.31\n", "\n", "        # Saisonnalité\n", "        temporal_features['first_order_month'] = seasonal_patterns['first_order_date'].dt.month\n", "        temporal_features['first_order_quarter'] = seasonal_patterns['first_order_date'].dt.quarter\n", "        temporal_features['first_order_weekday'] = seasonal_patterns['first_order_date'].dt.dayofweek\n", "\n", "        # <PERSON><PERSON><PERSON> d'achat\n", "        if 'frequency' in seasonal_patterns.columns:\n", "            temporal_features['purchase_frequency'] = (\n", "                seasonal_patterns['frequency'] / temporal_features['activity_period_days']\n", "            ).<PERSON>na(0)\n", "            temporal_features['purchase_frequency'] = temporal_features['purchase_frequency'].replace([np.inf, -np.inf], 0)\n", "\n", "        # Catégorisation de la période d'activité\n", "        temporal_features['activity_category'] = pd.cut(\n", "            temporal_features['activity_period_days'],\n", "            bins=[0, 30, 90, 180, 365, float('inf')],\n", "            labels=['Très court (<30j)', 'Court (30-90j)', '<PERSON><PERSON><PERSON> (90-180j)',\n", "                   'Long (180-365j)', 'Tr<PERSON> long (>365j)']\n", "        )\n", "\n", "        advanced_features = temporal_features\n", "\n", "        # Affichage des statistiques\n", "        print(\"\\n📊 Statistiques de période d'activité :\")\n", "        print(f\"- <PERSON><PERSON><PERSON><PERSON> moyenne : {temporal_features['activity_period_days'].mean():.1f} jours\")\n", "        print(f\"- <PERSON><PERSON><PERSON><PERSON> médiane : {temporal_features['activity_period_days'].median():.1f} jours\")\n", "        print(f\"- Période max : {temporal_features['activity_period_days'].max():.1f} jours\")\n", "\n", "        print(\"\\n📊 Distribution des catégories d'activité :\")\n", "        activity_dist = temporal_features['activity_category'].value_counts()\n", "        for category, count in activity_dist.items():\n", "            pct = (count / len(temporal_features)) * 100\n", "            print(f\"- {category}: {count} clients ({pct:.1f}%)\")\n", "\n", "        if 'purchase_frequency' in temporal_features.columns:\n", "            print(\"\\n📊 Statistiques de fréquence d'achat :\")\n", "            print(f\"- <PERSON><PERSON><PERSON> moyenne : {temporal_features['purchase_frequency'].mean():.3f} achats/jour\")\n", "            print(f\"- <PERSON><PERSON><PERSON> médiane : {temporal_features['purchase_frequency'].median():.3f} achats/jour\")\n", "\n", "    else:\n", "        print(\"⚠️ Colonnes nécessaires manquantes pour l'analyse temporelle\")\n", "\n", "    print(f\"\\n📊 Features avancées calculées pour {len(advanced_features):,} clients\")\n", "    display(advanced_features.head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Normalisation des variables\n", "\n", "### 4.1 Sélection des variables pour clustering"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Sélection des variables pour le clustering\n", "if 'rfm_enriched' in locals():\n", "    print(\"🔄 Sélection des variables pour clustering...\")\n", "\n", "    # Exclusion de customer_id et autres identifiants\n", "    exclude_cols = ['customer_id', 'first_order', 'last_order']\n", "\n", "    # Sélection automatique des colonnes numériques\n", "    numeric_cols = rfm_enriched.select_dtypes(include=[np.number]).columns.tolist()\n", "    clustering_features = [col for col in numeric_cols if col not in exclude_cols]\n", "\n", "    print(f\"📊 Variables sélectionnées pour clustering : {len(clustering_features)}\")\n", "    print(f\"📋 Variables : {clustering_features}\")\n", "\n", "    # Création du dataset pour clustering\n", "    X = rfm_enriched[clustering_features].copy()\n", "\n", "    # Vérification des valeurs manquantes\n", "    print(f\"\\n🔍 Valeurs manquantes par variable :\")\n", "    missing_counts = X.isnull().sum()\n", "    if missing_counts.sum() > 0:\n", "        print(missing_counts[missing_counts > 0])\n", "    else:\n", "        print(\"✅ Aucune valeur manquante\")\n", "\n", "    # Statistiques descriptives\n", "    print(f\"\\n📈 Statistiques descriptives :\")\n", "    display(X.describe())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.2 Traitement des valeurs manquantes avant normalisation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Traitement des valeurs manquantes et infinies\n", "if 'X' in locals():\n", "    print(\"🔄 Traitement des valeurs manquantes et infinies...\")\n", "\n", "    # Vérification des valeurs infinies\n", "    inf_check = np.isinf(X).sum()\n", "    if inf_check.sum() > 0:\n", "        print(f\"⚠️ Valeurs infinies détectées : {inf_check[inf_check > 0]}\")\n", "        # Remplacer les valeurs infinies par NaN\n", "        X = X.replace([np.inf, -np.inf], np.nan)\n", "\n", "    # Nettoyage des colonnes en double\n", "    print(\"\\n🧹 Nettoyage des colonnes en double...\")\n", "    duplicate_cols = X.columns[X.columns.duplicated()].tolist()\n", "    if duplicate_cols:\n", "        print(f\"Colonnes en double détectées : {duplicate_cols}\")\n", "        # Garder la première occurrence de chaque colonne\n", "        X = X.loc[:, ~X.columns.duplicated()]\n", "        print(f\"Nombre de colonnes après nettoyage : {len(X.columns)}\")\n", "\n", "    # Vérification des colonnes avant imputation\n", "    print(\"\\n📊 Colonnes avant imputation :\")\n", "    print(f\"Nombre de colonnes : {len(X.columns)}\")\n", "    print(\"Liste des colonnes :\")\n", "    print(X.columns.tolist())\n", "\n", "    # Imputation des valeurs manquantes\n", "    if <PERSON><PERSON>isnull().sum().sum() > 0:\n", "        print(\"\\n🔄 Imputation des valeurs manquantes avec la médiane...\")\n", "\n", "        # Vérification des types de données\n", "        print(\"\\n📊 Types de données avant conversion :\")\n", "        print(X.dtypes)\n", "\n", "        # Conversion explicite des colonnes en types numériques\n", "        for col in X.columns:\n", "            try:\n", "                X[col] = pd.to_numeric(X[col], errors='coerce')\n", "            except Exception as e:\n", "                print(f\"⚠️ Erreur lors de la conversion de {col}: {str(e)}\")\n", "\n", "        # Vérification des valeurs manquantes par colonne\n", "        print(\"\\n📊 Valeurs manquantes par colonne :\")\n", "        missing_values = X.isnull().sum()\n", "        print(missing_values[missing_values > 0])\n", "\n", "        # Imputation directe avec la médiane\n", "        X_imputed = X.copy()\n", "        for col in X_imputed.columns:\n", "            median_value = X_imputed[col].median()\n", "            X_imputed[col] = X_imputed[col].fillna(median_value)\n", "\n", "        # Vérification des colonnes après imputation\n", "        print(\"\\n📊 Colonnes après imputation :\")\n", "        print(f\"Nombre de colonnes : {len(X_imputed.columns)}\")\n", "        print(\"Liste des colonnes :\")\n", "        print(X_imputed.columns.tolist())\n", "\n", "        print(f\"\\n✅ Valeurs manquantes après imputation : {X_imputed.isnull().sum().sum()}\")\n", "    else:\n", "        X_imputed = X.copy()\n", "        print(\"✅ Aucune imputation nécessaire\")\n", "\n", "    print(f\"\\n📊 Dataset préparé pour normalisation : {X_imputed.shape}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.3 Standardisation via StandardScaler"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Standardisation des variables\n", "if 'X_imputed' in locals():\n", "    print(\"🔄 Standardisation des variables...\")\n", "\n", "    # Identification des variables à normaliser (écart-type > 0)\n", "    variables_to_scale = X_imputed.columns[X_imputed.std() > 0].tolist()\n", "    constant_variables = X_imputed.columns[X_imputed.std() == 0].tolist()\n", "\n", "    print(f\"\\n📊 Variables à normaliser : {len(variables_to_scale)}\")\n", "    print(f\"📊 Variables constantes : {len(constant_variables)}\")\n", "\n", "    # Création d'une copie du DataFrame\n", "    X_scaled = X_imputed.copy()\n", "\n", "    # Normalisation uniquement des variables non constantes\n", "    if variables_to_scale:\n", "        scaler = StandardScaler()\n", "        X_scaled[variables_to_scale] = scaler.fit_transform(X_imputed[variables_to_scale])\n", "\n", "    # Vérification des valeurs manquantes après standardisation\n", "    print(\"\\n📊 Vérification des valeurs manquantes après standardisation :\")\n", "    missing_after = X_scaled.isnull().sum()\n", "    print(missing_after[missing_after > 0])\n", "\n", "    # Remplacement des valeurs NaN par 0 (ou une autre stratégie appropriée)\n", "    X_scaled = X_scaled.fillna(0)\n", "\n", "    print(f\"\\n📊 Dataset normalisé : {X_scaled.shape}\")\n", "    print(f\"\\n📈 Vérification de la standardisation :\")\n", "    print(f\"- <PERSON><PERSON><PERSON> (doivent être ~0) : {X_scaled[variables_to_scale].mean().mean():.6f}\")\n", "    print(f\"- Écarts-types (doivent être ~1) : {X_scaled[variables_to_scale].std().mean():.6f}\")\n", "\n", "    # Affichage des statistiques par variable\n", "    print(\"\\n📊 Statistiques par variable :\")\n", "    stats_df = pd.DataFrame({\n", "        'Moyenne': X_scaled.mean(),\n", "        'Écart-type': X_scaled.std(),\n", "        'Normalisée': X_scaled.columns.isin(variables_to_scale)\n", "    })\n", "    display(stats_df)\n", "\n", "    # Visualisation des distributions après normalisation\n", "    n_features = min(12, len(variables_to_scale))\n", "    n_rows = (n_features + 3) // 4\n", "\n", "    fig, axes = plt.subplots(n_rows, 4, figsize=(20, 5*n_rows))\n", "    if n_rows == 1:\n", "        axes = axes.reshape(1, -1)\n", "    axes = axes.ravel()\n", "\n", "    for i, col in enumerate(variables_to_scale[:n_features]):\n", "        # Vérification des valeurs avant tracé\n", "        values = X_scaled[col].dropna()\n", "        if len(values) > 0:\n", "            axes[i].hist(values, bins=30, alpha=0.7, color='skyblue', edgecolor='black')\n", "            axes[i].set_title(f'{col} (normalisé)', fontsize=10)\n", "            axes[i].axvline(0, color='red', linestyle='--', alpha=0.7, label='Moyenne=0')\n", "            axes[i].grid(True, alpha=0.3)\n", "            axes[i].legend()\n", "        else:\n", "            axes[i].text(0.5, 0.5, f'Pas de données valides\\npour {col}',\n", "                        horizontalalignment='center', verticalalignment='center')\n", "            axes[i].set_title(f'{col} (normalisé)', fontsize=10)\n", "\n", "    # Masquer les axes non utilisés\n", "    for i in range(n_features, len(axes)):\n", "        axes[i].set_visible(False)\n", "\n", "    plt.suptitle('Distributions des Variables Normalisées', fontsize=16, fontweight='bold')\n", "    plt.tight_layout()\n", "\n", "    # Export de la figure selon les règles du projet\n", "    export_figure(fig, notebook_name=\"2\", export_number=2, base_name=\"normalized_distributions\")\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.4 Export et backup des variables d'origine"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Export et backup des variables d'origine et normalisées\n", "if 'X_scaled' in locals() and 'rfm_enriched' in locals():\n", "    print(\"💾 Préparation des datasets finaux...\")\n", "\n", "    # Sauvegarde du dataset complet avec versions originales et normalisées\n", "    rfm_final = rfm_enriched.copy()\n", "\n", "    # Ajout des versions normalisées avec suffixe '_scaled'\n", "    for col in X_scaled.columns:\n", "        if col in rfm_final.columns:\n", "            rfm_final[f'{col}_scaled'] = X_scaled[col]\n", "\n", "    # Dataset pour clustering avec customer_id pour traçabilité\n", "    X_scaled_with_id = X_scaled.copy()\n", "    X_scaled_with_id['customer_id'] = rfm_enriched['customer_id'].values\n", "\n", "    # Statistiques finales\n", "    print(f\"📊 Dataset final pour clustering : {X_scaled.shape}\")\n", "    print(f\"📊 Dataset complet avec IDs : {X_scaled_with_id.shape}\")\n", "    print(f\"📊 Dataset enrichi original : {rfm_final.shape}\")\n", "\n", "    # Résumé des features créées\n", "    print(f\"\\n📋 Résumé des features créées :\")\n", "    feature_types = {\n", "        'RFM de base': [col for col in rfm_final.columns if any(x in col.lower() for x in ['recency', 'frequency', 'monetary', 'recence', 'frequence', 'montant'])],\n", "        'Temporelles': [col for col in rfm_final.columns if any(x in col.lower() for x in ['lifespan', 'days_since', 'interval', 'between'])],\n", "        'Variabilité': [col for col in rfm_final.columns if any(x in col.lower() for x in ['std', 'cv', 'min', 'max', 'range'])],\n", "        'Diversité': [col for col in rfm_final.columns if any(x in col.lower() for x in ['unique', 'categories', 'diversity'])],\n", "        'Avancées': [col for col in rfm_final.columns if any(x in col.lower() for x in ['cancellation', 'delivery', 'quarter', 'seasonal'])],\n", "        'Ratios': [col for col in rfm_final.columns if any(x in col.lower() for x in ['per_', 'ratio', '_per_'])]\n", "    }\n", "\n", "    for feature_type, features in feature_types.items():\n", "        if features:\n", "            print(f\"- {feature_type}: {len(features)} features\")\n", "\n", "    print(f\"\\n✅ Total: {len(rfm_final.columns)} variables dans le dataset complet\")\n", "    print(f\"✅ Variables pour clustering: {len(X_scaled.columns)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Nettoyage final et Sauvegarde\n", "\n", "### 5.1 Nettoyage final (verif)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Nettoyage des valeurs infinies et manquantes dans X_scaled\n", "X_scaled.replace([np.inf, -np.inf], np.nan, inplace=True)\n", "\n", "print(\"Valeurs manquantes avant traitement :\", X_scaled.isna().sum().sum())\n", "print(\"Valeurs infinies avant traitement :\", np.isinf(X_scaled.values).sum())\n", "\n", "# Option 1 : <PERSON><PERSON><PERSON><PERSON> les NaN par 0 (ou une autre valeur pertinente)\n", "X_scaled.fillna(0, inplace=True)\n", "\n", "# Option 2 (alternative) : Su<PERSON><PERSON>er les lignes avec NaN\n", "# X_scaled.dropna(inplace=True)\n", "\n", "print(\"Valeurs manquantes après traitement :\", X_scaled.isna().sum().sum())\n", "print(\"Valeurs infinies après traitement :\", np.isinf(X_scaled.values).sum())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5.2 Export du jeu final prêt à clusteriser"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Export des datasets pour le clustering\n", "if 'X_scaled' in locals() and 'rfm_enriched' in locals():\n", "    print(\"💾 Export des datasets pour le clustering...\")\n", "\n", "    # Création du dossier de sortie\n", "    os.makedirs('data/processed', exist_ok=True)\n", "\n", "    # Dataset normalisé pour clustering (sans customer_id)\n", "    clustering_data_path = 'data/processed/2_01_features_scaled_clustering.csv'\n", "    X_scaled.to_csv(clustering_data_path, index=False)\n", "    print(f\"✅ Dataset clustering sauvegardé : {clustering_data_path}\")\n", "\n", "    # Dataset avec customer_id pour traçabilité\n", "    X_scaled_with_id = X_scaled.copy()\n", "    X_scaled_with_id['customer_id'] = rfm_enriched['customer_id']\n", "\n", "    traceability_path = 'data/processed/2_02_features_scaled_with_ids.csv'\n", "    X_scaled_with_id.to_csv(traceability_path, index=False)\n", "    print(f\"✅ Dataset avec IDs sauvegardé : {traceability_path}\")\n", "\n", "    # Dataset complet enrichi avec versions originales et normalisées\n", "    if 'rfm_final' in locals():\n", "        complete_path = 'data/processed/2_03_rfm_enriched_complete.csv'\n", "        rfm_final.to_csv(complete_path, index=False)\n", "        print(f\"✅ Dataset complet final sauvegardé : {complete_path}\")\n", "    else:\n", "        # Fallback vers rfm_enriched si rfm_final n'existe pas\n", "        complete_path = 'data/processed/2_03_rfm_enriched_complete.csv'\n", "        rfm_enriched.to_csv(complete_path, index=False)\n", "        print(f\"✅ Dataset enrichi sauvegardé : {complete_path}\")\n", "\n", "    # Sauvegarde du scaler pour usage futur\n", "    if 'scaler' in locals():\n", "        import joblib\n", "        scaler_path = 'data/processed/2_04_scaler.joblib'\n", "        joblib.dump(scaler, scaler_path)\n", "        print(f\"✅ Scaler sauvegardé : {scaler_path}\")\n", "\n", "    print(f\"\\n📊 Résumé des exports :\")\n", "    print(f\"- Dataset clustering : {X_scaled.shape} - {clustering_data_path}\")\n", "    if 'X_scaled_with_id' in locals():\n", "        print(f\"- Dataset avec IDs : {X_scaled_with_id.shape} - {traceability_path}\")\n", "    if 'rfm_final' in locals():\n", "        print(f\"- Dataset complet final : {rfm_final.shape} - {complete_path}\")\n", "    else:\n", "        print(f\"- Dataset enrichi : {rfm_enriched.shape} - {complete_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5.3 Export de la liste des variables utilisées"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Export de la configuration des features\n", "if 'X_scaled' in locals() and 'rfm_enriched' in locals():\n", "    print(\"📝 Export de la configuration des features...\")\n", "\n", "    # Création de la configuration\n", "    feature_config = {\n", "        'notebook': '2_feature_engineering',\n", "        'date_processing': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),\n", "        'reference_date': reference_date.strftime('%Y-%m-%d') if 'reference_date' in locals() else None,\n", "        'clustering_features': list(X_scaled.columns),\n", "        'original_shape': list(rfm_enriched.shape),\n", "        'final_shape': list(X_scaled.shape),\n", "        'normalization_method': 'StandardScaler',\n", "        'imputation_strategy': 'median',\n", "        'n_customers': len(rfm_enriched),\n", "        'n_features': len(X_scaled.columns),\n", "        'feature_descriptions': {\n", "            'recency': '<PERSON><PERSON> depuis le dernier achat',\n", "            'frequency': 'Nombre total d\\'achats',\n", "            'monetary': '<PERSON><PERSON> des achats',\n", "            'customer_lifespan_days': '<PERSON><PERSON>e entre premier et dernier achat',\n", "            'days_since_first_order': 'Jo<PERSON> depuis le premier achat',\n", "            'avg_days_between_orders': '<PERSON><PERSON><PERSON> moy<PERSON> entre commandes',\n", "            'order_std': 'Écart-type des montants',\n", "            'order_cv': 'Coefficient de variation des montants',\n", "            'monetary_per_frequency': 'Montant total / fréquence',\n", "            'frequency_per_day': 'Fréquence normalisée par jour'\n", "        },\n", "        'files_exported': {\n", "            'clustering_data': '2_01_features_scaled_clustering.csv',\n", "            'data_with_ids': '2_02_features_scaled_with_ids.csv',\n", "            'complete_data': '2_03_rfm_enriched_complete.csv',\n", "            'scaler': '2_04_scaler.joblib'\n", "        }\n", "    }\n", "\n", "    # Sauvegarde de la configuration\n", "    config_path = 'data/processed/2_05_feature_engineering_config.json'\n", "    with open(config_path, 'w', encoding='utf-8') as f:\n", "        json.dump(feature_config, f, indent=2, ensure_ascii=False, default=str)\n", "\n", "    print(f\"✅ Configuration sauvegardée : {config_path}\")\n", "    print(f\"📊 Variables finales pour clustering : {len(X_scaled.columns)}\")\n", "    print(f\"👥 Nombre de clients : {len(rfm_enriched):,}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Conclusion\n", "\n", "### Résumé des étapes réalisées\n", "- ✅ **Section 1 :** Chargement des données nettoyées du Notebook 1 avec modules utils\n", "- ✅ **Section 2 :** Calcul des variables RFM (Ré<PERSON>, Fréquence, Montant) avec visualisations\n", "- ✅ **Section 3 :** Enrichissement comportemental complet :\n", "  - 3.1 : Ancienneté client et features temporelles\n", "  - 3.2 : <PERSON><PERSON><PERSON> entre commandes et features transactionnelles\n", "  - 3.3 : Diversité des achats et variabilité des montants\n", "  - 3.4 : Consolidation de toutes les features\n", "  - 3.5 : <PERSON><PERSON> de retour et indicateurs avancés (saisonnalité)\n", "- ✅ **Section 4 :** Normalisation et préparation pour clustering :\n", "  - 4.1 : Sélection automatique des variables\n", "  - 4.2 : Traitement des valeurs manquantes et infinies\n", "  - 4.3 : Standardisation avec visualisations\n", "  - 4.4 : Export et backup des versions originales et normalisées\n", "- ✅ **Section 5 :** Sauvegarde complète avec convention de nommage du projet\n", "\n", "### Variables créées pour la segmentation\n", "- **Variables RFM classiques :** récence, fréquence, montant total/moyen\n", "- **Variables temporelles :** ancie<PERSON><PERSON>, d<PERSON><PERSON>s entre commandes, diversité temporelle\n", "- **Variables de variabilité :** écart-type, coefficient de variation, min/max/range\n", "- **Variables de diversité :** catégories uniques, variabilité des achats\n", "- **Variables avancées :** taux d'annulation, livraison, saisonnalité\n", "- **Variables de ratio :** montant/fréquence, fréquence/jour\n", "\n", "### Prochaines étapes\n", "➡️ **Notebook 3 :** Clustering et segmentation avec les variables préparées\n", "\n", "---\n", "\n", "**Dataset enrichi et normalisé prêt pour la segmentation !**"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}